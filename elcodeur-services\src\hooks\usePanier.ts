import { useState, useEffect } from 'react'

interface PanierItem {
  id: string
  title: string
  price: number
  quantity: number
  description: string
}

export const usePanier = () => {
  const [items, setItems] = useState<PanierItem[]>([])

  // Charger le panier depuis localStorage au démarrage
  useEffect(() => {
    const savedPanier = localStorage.getItem('elcodeur-panier')
    if (savedPanier) {
      try {
        setItems(JSON.parse(savedPanier))
      } catch (error) {
        console.error('Erreur lors du chargement du panier:', error)
      }
    }
  }, [])

  // Sauvegarder le panier dans localStorage à chaque changement
  useEffect(() => {
    localStorage.setItem('elcodeur-panier', JSON.stringify(items))
  }, [items])

  // Ajouter un item au panier
  const addItem = (newItem: Omit<PanierItem, 'quantity'>) => {
    setItems(prevItems => {
      const existingItem = prevItems.find(item => item.id === newItem.id)
      
      if (existingItem) {
        // Si l'item existe déjà, augmenter la quantité
        return prevItems.map(item =>
          item.id === newItem.id
            ? { ...item, quantity: item.quantity + 1 }
            : item
        )
      } else {
        // Sinon, ajouter le nouvel item avec quantité 1
        return [...prevItems, { ...newItem, quantity: 1 }]
      }
    })
  }

  // Mettre à jour la quantité d'un item
  const updateQuantity = (id: string, quantity: number) => {
    if (quantity <= 0) {
      removeItem(id)
      return
    }

    setItems(prevItems =>
      prevItems.map(item =>
        item.id === id ? { ...item, quantity } : item
      )
    )
  }

  // Supprimer un item du panier
  const removeItem = (id: string) => {
    setItems(prevItems => prevItems.filter(item => item.id !== id))
  }

  // Vider le panier
  const clearPanier = () => {
    setItems([])
  }

  // Calculer le total
  const getTotalAmount = () => {
    return items.reduce((total, item) => total + (item.price * item.quantity), 0)
  }

  // Calculer le nombre total d'items
  const getTotalItems = () => {
    return items.reduce((total, item) => total + item.quantity, 0)
  }

  return {
    items,
    addItem,
    updateQuantity,
    removeItem,
    clearPanier,
    getTotalAmount,
    getTotalItems
  }
}
