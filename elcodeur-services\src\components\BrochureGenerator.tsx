import React from 'react'

interface BrochureGeneratorProps {
  onDownload: () => void
}

const BrochureGenerator: React.FC<BrochureGeneratorProps> = ({ onDownload }) => {
  
  const generateBrochure = () => {
    // Contenu HTML de la brochure optimisé pour PDF
    const brochureContent = `
<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Brochure elCodeur Services</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Arial', sans-serif;
            line-height: 1.6;
            color: #333;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
        
        .brochure {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
        }
        
        .header {
            background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
            color: white;
            padding: 40px;
            text-align: center;
            position: relative;
            overflow: hidden;
        }
        
        .header::before {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><circle cx="50" cy="50" r="2" fill="rgba(255,255,255,0.1)"/></svg>') repeat;
            animation: float 20s infinite linear;
        }
        
        @keyframes float {
            0% { transform: translate(-50%, -50%) rotate(0deg); }
            100% { transform: translate(-50%, -50%) rotate(360deg); }
        }
        
        .logo {
            font-size: 2.5em;
            font-weight: bold;
            margin-bottom: 10px;
            position: relative;
            z-index: 2;
        }
        
        .tagline {
            font-size: 1.2em;
            opacity: 0.9;
            position: relative;
            z-index: 2;
        }
        
        .section {
            padding: 30px 40px;
        }
        
        .section-title {
            font-size: 1.8em;
            color: #1e3c72;
            margin-bottom: 20px;
            border-bottom: 3px solid #2a5298;
            padding-bottom: 10px;
        }
        
        .intro {
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
            text-align: center;
        }
        
        .intro p {
            font-size: 1.1em;
            color: #555;
            max-width: 600px;
            margin: 0 auto;
        }
        
        .services-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 25px;
            margin-top: 20px;
        }
        
        .service-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 25px;
            border-radius: 15px;
            box-shadow: 0 10px 25px rgba(0,0,0,0.1);
            transition: transform 0.3s ease;
        }
        
        .service-card:hover {
            transform: translateY(-5px);
        }
        
        .service-icon {
            font-size: 2.5em;
            margin-bottom: 15px;
            opacity: 0.9;
        }
        
        .service-title {
            font-size: 1.3em;
            font-weight: bold;
            margin-bottom: 10px;
        }
        
        .service-price {
            font-size: 1.1em;
            font-weight: bold;
            color: #ffd700;
            margin-bottom: 10px;
        }
        
        .service-features {
            list-style: none;
            margin-top: 15px;
        }
        
        .service-features li {
            padding: 3px 0;
            position: relative;
            padding-left: 20px;
        }
        
        .service-features li::before {
            content: '✓';
            position: absolute;
            left: 0;
            color: #4ade80;
            font-weight: bold;
        }
        
        .formations-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }
        
        .formation-card {
            background: linear-gradient(135deg, #11998e 0%, #38ef7d 100%);
            color: white;
            padding: 20px;
            border-radius: 12px;
            text-align: center;
            position: relative;
            overflow: hidden;
        }
        
        .formation-card::before {
            content: 'PROMO';
            position: absolute;
            top: 10px;
            right: -25px;
            background: #ef4444;
            color: white;
            padding: 5px 30px;
            font-size: 0.8em;
            font-weight: bold;
            transform: rotate(45deg);
        }
        
        .formation-title {
            font-size: 1.2em;
            font-weight: bold;
            margin-bottom: 10px;
        }
        
        .formation-price {
            font-size: 1.4em;
            font-weight: bold;
            color: #ffd700;
            margin-bottom: 5px;
        }
        
        .formation-duration {
            opacity: 0.9;
            margin-bottom: 15px;
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }
        
        .stat-card {
            background: white;
            padding: 20px;
            border-radius: 10px;
            text-align: center;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            border-left: 4px solid #2a5298;
        }
        
        .stat-number {
            font-size: 2em;
            font-weight: bold;
            color: #2a5298;
        }
        
        .stat-label {
            color: #666;
            font-size: 0.9em;
        }
        
        .contact-info {
            background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
            color: white;
            text-align: center;
        }
        
        .contact-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }
        
        .contact-item {
            padding: 15px;
            background: rgba(255,255,255,0.1);
            border-radius: 10px;
            backdrop-filter: blur(10px);
        }
        
        .contact-icon {
            font-size: 1.5em;
            margin-bottom: 10px;
        }
        
        .footer {
            background: #1a1a1a;
            color: white;
            text-align: center;
            padding: 20px;
            font-size: 0.9em;
        }
        
        .cta-section {
            background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
            color: white;
            text-align: center;
            padding: 30px;
        }
        
        .cta-title {
            font-size: 1.5em;
            margin-bottom: 15px;
        }
        
        .cta-button {
            display: inline-block;
            background: white;
            color: #ee5a24;
            padding: 12px 30px;
            border-radius: 25px;
            text-decoration: none;
            font-weight: bold;
            margin-top: 15px;
            transition: transform 0.3s ease;
        }
        
        .cta-button:hover {
            transform: scale(1.05);
        }
        
        @media print {
            body { background: white; }
            .brochure { box-shadow: none; }
            .no-print { display: none; }
        }

        .print-button {
            position: fixed;
            top: 20px;
            right: 20px;
            background: #ef4444;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            font-weight: bold;
            cursor: pointer;
            box-shadow: 0 4px 12px rgba(0,0,0,0.2);
            z-index: 1000;
        }

        .print-button:hover {
            background: #dc2626;
            transform: translateY(-2px);
        }
    </style>
</head>
<body>
    <!-- Instructions et bouton d'impression/téléchargement PDF -->
    <div class="no-print" style="position: fixed; top: 0; left: 0; right: 0; background: rgba(0,0,0,0.9); color: white; padding: 15px; text-align: center; z-index: 1001;">
        <p style="margin: 0 0 10px 0;">📄 <strong>Brochure elCodeur Services</strong> - Cliquez sur le bouton rouge pour télécharger en PDF</p>
    </div>

    <button class="print-button no-print" onclick="window.print()" title="Télécharger cette brochure en PDF">
        📄 Télécharger en PDF
    </button>

    <div class="brochure">
        <!-- En-tête -->
        <div class="header">
            <div class="logo">elCodeur Services</div>
            <div class="tagline">Votre partenaire digital de confiance</div>
        </div>
        
        <!-- Introduction -->
        <div class="section intro">
            <h2 class="section-title">Qui sommes-nous ?</h2>
            <p>
                elCodeur Services est une entreprise spécialisée dans le développement de solutions digitales innovantes. 
                Fondée par TOMEKPA ELIE MAHOUAN, nous transformons vos idées en réalités numériques avec expertise et passion. 
                Notre équipe d'experts vous accompagne dans votre transformation digitale avec des solutions sur mesure.
            </p>
        </div>
        
        <!-- Services -->
        <div class="section">
            <h2 class="section-title">Nos Services</h2>
            <div class="services-grid">
                <div class="service-card">
                    <div class="service-icon">🌐</div>
                    <div class="service-title">Site Vitrine</div>
                    <div class="service-price">250 000 FCFA</div>
                    <p>Présentez votre entreprise avec un site web professionnel et attractif.</p>
                    <ul class="service-features">
                        <li>Design personnalisé</li>
                        <li>Responsive design</li>
                        <li>Optimisation SEO</li>
                        <li>Formulaire de contact</li>
                    </ul>
                </div>
                
                <div class="service-card">
                    <div class="service-icon">🛒</div>
                    <div class="service-title">Site E-commerce</div>
                    <div class="service-price">450 000 FCFA</div>
                    <p>Vendez vos produits en ligne avec une boutique digitale complète.</p>
                    <ul class="service-features">
                        <li>Catalogue produits</li>
                        <li>Panier d'achat</li>
                        <li>Paiement sécurisé</li>
                        <li>Gestion des stocks</li>
                    </ul>
                </div>
                
                <div class="service-card">
                    <div class="service-icon">⚙️</div>
                    <div class="service-title">Application Web</div>
                    <div class="service-price">800 000 FCFA</div>
                    <p>Solutions web sur mesure pour des besoins spécifiques et complexes.</p>
                    <ul class="service-features">
                        <li>Développement sur mesure</li>
                        <li>Interface intuitive</li>
                        <li>Base de données sécurisée</li>
                        <li>API et intégrations</li>
                    </ul>
                </div>
                
                <div class="service-card">
                    <div class="service-icon">📱</div>
                    <div class="service-title">Application Mobile</div>
                    <div class="service-price">600 000 FCFA</div>
                    <p>Applications mobiles natives et cross-platform pour iOS et Android.</p>
                    <ul class="service-features">
                        <li>Apps natives iOS/Android</li>
                        <li>Cross-platform</li>
                        <li>Interface moderne</li>
                        <li>Publication stores</li>
                    </ul>
                </div>
                
                <div class="service-card">
                    <div class="service-icon">☁️</div>
                    <div class="service-title">Solutions Cloud</div>
                    <div class="service-price">350 000 FCFA</div>
                    <p>Migration et déploiement vers le cloud pour plus de performance.</p>
                    <ul class="service-features">
                        <li>Migration cloud</li>
                        <li>Hébergement sécurisé</li>
                        <li>Sauvegarde automatique</li>
                        <li>Monitoring 24/7</li>
                    </ul>
                </div>
                
                <div class="service-card">
                    <div class="service-icon">🛡️</div>
                    <div class="service-title">Sécurité Web</div>
                    <div class="service-price">200 000 FCFA</div>
                    <p>Audit et renforcement de la sécurité de vos applications web.</p>
                    <ul class="service-features">
                        <li>Audit de sécurité</li>
                        <li>Certificats SSL</li>
                        <li>Protection attaques</li>
                        <li>Monitoring sécurité</li>
                    </ul>
                </div>
            </div>
        </div>
        
        <!-- Formations -->
        <div class="section">
            <h2 class="section-title">Formations en Promo</h2>
            <div class="formations-grid">
                <div class="formation-card">
                    <div class="formation-title">Formation Infographie</div>
                    <div class="formation-price">89 000 FCFA</div>
                    <div class="formation-duration">6 semaines</div>
                    <p>Maîtrisez Photoshop, Illustrator et InDesign</p>
                </div>
                
                <div class="formation-card">
                    <div class="formation-title">Administration Web</div>
                    <div class="formation-price">75 000 FCFA</div>
                    <div class="formation-duration">4 semaines</div>
                    <p>Gérez et maintenez des sites web professionnels</p>
                </div>
                
                <div class="formation-card">
                    <div class="formation-title">CV Pro</div>
                    <div class="formation-price">45 000 FCFA</div>
                    <div class="formation-duration">2 semaines</div>
                    <p>CV moderne et optimisation LinkedIn</p>
                </div>
            </div>
        </div>
        
        <!-- Statistiques -->
        <div class="section">
            <h2 class="section-title">Nos Résultats</h2>
            <div class="stats-grid">
                <div class="stat-card">
                    <div class="stat-number">150+</div>
                    <div class="stat-label">Projets réalisés</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number">98%</div>
                    <div class="stat-label">Clients satisfaits</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number">665+</div>
                    <div class="stat-label">Étudiants formés</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number">24h</div>
                    <div class="stat-label">Temps de réponse</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number">8+</div>
                    <div class="stat-label">Partenaires actifs</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number">5 ans</div>
                    <div class="stat-label">Expérience moyenne</div>
                </div>
            </div>
        </div>
        
        <!-- Call to Action -->
        <div class="cta-section">
            <div class="cta-title">Prêt à transformer votre présence digitale ?</div>
            <p>Contactez-nous dès aujourd'hui pour discuter de votre projet et obtenir un devis personnalisé.</p>
            <a href="#" class="cta-button">Demander un Devis Gratuit</a>
        </div>
        
        <!-- Contact -->
        <div class="section contact-info">
            <h2 class="section-title">Contactez-nous</h2>
            <div class="contact-grid">
                <div class="contact-item">
                    <div class="contact-icon">📞</div>
                    <div>+225 05 56 75 04 67</div>
                </div>
                <div class="contact-item">
                    <div class="contact-icon">✉️</div>
                    <div><EMAIL></div>
                </div>
                <div class="contact-item">
                    <div class="contact-icon">🌐</div>
                    <div>www.elcodeur-services.com</div>
                </div>
                <div class="contact-item">
                    <div class="contact-icon">📍</div>
                    <div>Abidjan, Côte d'Ivoire</div>
                </div>
            </div>
        </div>
        
        <!-- Footer -->
        <div class="footer">
            <p>&copy; 2024 elCodeur Services. Tous droits réservés. | Votre partenaire digital de confiance</p>
        </div>
    </div>
</body>
</html>
    `

    // Créer un blob avec le contenu HTML
    const blob = new Blob([brochureContent], { type: 'text/html' })
    const url = URL.createObjectURL(blob)

    // Ouvrir dans un nouvel onglet
    const newWindow = window.open(url, '_blank')

    if (newWindow) {
      // Nettoyer l'URL après un délai
      setTimeout(() => {
        URL.revokeObjectURL(url)
      }, 5000)

      // Appeler la fonction de callback
      onDownload()
    } else {
      // Fallback si le popup est bloqué - téléchargement direct
      const link = document.createElement('a')
      link.href = url
      link.download = 'Brochure_elCodeur_Services.html'
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)
      URL.revokeObjectURL(url)
      onDownload()
    }
  }

  return (
    <button
      onClick={generateBrochure}
      className="bg-red-600 hover:bg-red-700 text-white px-6 py-3 rounded-full font-medium transition-all rounded-button cursor-pointer whitespace-nowrap flex items-center"
    >
      <i className="fas fa-file-pdf mr-2"></i>
      Télécharger notre brochure
    </button>
  )
}

export default BrochureGenerator
