import React from 'react'

const CTA: React.FC = () => {
  return (
    <section className="py-16 bg-gradient-to-r from-blue-800 to-blue-900 text-white relative">
      <div className="container mx-auto px-4 text-center">
        <h2 className="text-3xl font-bold mb-6">Prêt à concrétiser votre projet ?</h2>
        <p className="text-xl mb-8 max-w-2xl mx-auto">
          Contactez-nous dès aujourd'hui pour discuter de vos besoins et obtenir un devis personnalisé.
        </p>
        <div className="flex flex-wrap justify-center gap-4">
          <a
            href="tel:+22507592645"
            className="flex items-center bg-white text-blue-800 px-6 py-3 rounded-full font-medium transition-all hover:bg-blue-50 rounded-button cursor-pointer whitespace-nowrap no-underline"
          >
            <i className="fas fa-phone-alt mr-2"></i>+225 05 56 75 04 67
          </a>
          <a
            href="mailto:<EMAIL>"
            className="flex items-center bg-red-600 hover:bg-red-700 text-white px-6 py-3 rounded-full font-medium transition-all rounded-button cursor-pointer whitespace-nowrap no-underline"
          >
            <i className="fas fa-envelope mr-2"></i>Nous contacter par email
          </a>
        </div>
      </div>

      {/* Logo dans un cercle blanc à gauche */}
      <div className="hidden md:flex absolute top-1/2 left-4 transform -translate-y-1/2">
        <div className="hero-logo-circle w-32 h-32 lg:w-36 lg:h-36 bg-white rounded-full flex items-center justify-center shadow-2xl">
          <img
            src="/logo.gif"
            alt="elCodeur Services Logo"
            className="w-32 h-32 lg:w-36 lg:h-36 object-contain"
          />
        </div>
      </div>

      {/* Icône scroll vers navigation avec animation */}
      <button
        onClick={() => {
          const header = document.querySelector('header');
          if (header) {
            header.scrollIntoView({ behavior: 'smooth' });
          }
        }}
        className="absolute top-4 right-4 text-white hover:text-blue-200 transition-colors duration-300 cursor-pointer"
        title="Remonter vers la navigation"
      >
        <i className="fas fa-angle-double-up text-xl bounce-up-animation"></i>
      </button>
    </section>
  )
}

export default CTA
