import React from 'react'
import ServiceCard from './ServiceCard'
import TechStack from './TechStack'
import PricingGrid from './PricingGrid'

interface ServicesSectionProps {
  onAddToPanier: (item: {
    id: string
    title: string
    price: number
    description: string
  }) => void
}

const ServicesSection: React.FC<ServicesSectionProps> = ({ onAddToPanier }) => {
  return (
    <section className="py-16">
      <div className="container mx-auto px-4">
        {/* Main Service Description */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 mb-16">
          <div>
            <h2 className="text-3xl font-bold mb-6 flex items-center">
              <span className="text-red-600">DÉVELOPPEMENT</span>
              <span className="text-blue-800 ml-2">D'APPLICATIONS</span>
            </h2>
            <p className="text-gray-700 mb-6">
              Notre équipe de développeurs de solutions informatique et innovante expérimentés crée des sites internet et applications web sur mesure, 
              adaptés à vos besoins spécifiques. Nous combinons expertise technique et design attrayant pour offrir 
              des solutions digitales performantes qui vous démarquent de la concurrence.
            </p>
            <div className="bg-blue-50 border-l-4 border-blue-800 p-4 mb-6">
              <h3 className="font-bold text-blue-800 mb-2">Notre expertise technique</h3>
              <ul className="space-y-2">
                <li className="flex items-start">
                  <i className="fas fa-check-circle text-blue-800 mt-1 mr-2"></i>
                  <span>Sites vitrines, e-commerce et applications web</span>
                </li>
                <li className="flex items-start">
                  <i className="fas fa-check-circle text-blue-800 mt-1 mr-2"></i>
                  <span>Design responsive pour tous les appareils</span>
                </li>
                <li className="flex items-start">
                  <i className="fas fa-check-circle text-blue-800 mt-1 mr-2"></i>
                  <span>Optimisation SEO pour un meilleur référencement</span>
                </li>
                <li className="flex items-start">
                  <i className="fas fa-check-circle text-blue-800 mt-1 mr-2"></i>
                  <span>Maintenance et support technique continus</span>
                </li>
              </ul>
            </div>
            <div className="flex flex-wrap gap-4">
              <button
                onClick={() => {
                  const portfolioSection = document.querySelector('[data-section="portfolio"]');
                  if (portfolioSection) {
                    portfolioSection.scrollIntoView({ behavior: 'smooth' });
                  }
                }}
                className="bg-blue-800 hover:bg-blue-900 text-white px-6 py-3 rounded-full font-medium transition-all rounded-button cursor-pointer whitespace-nowrap"
              >
                <i className="fas fa-laptop-code mr-2"></i> Voir nos réalisations
              </button>
              <button className="bg-red-600 hover:bg-red-700 text-white px-6 py-3 rounded-full font-medium transition-all rounded-button cursor-pointer whitespace-nowrap">
                <i className="fas fa-file-pdf mr-2"></i> Télécharger notre brochure
              </button>
            </div>
          </div>
          <div>
            <img
              src="/plyer.jpg"
              alt="Développement web professionnel"
              className="w-full h-full object-cover rounded-lg shadow-lg"
            />
          </div>
        </div>

        {/* Service Cards */}
        <div className="mb-16">
          <h3 className="text-2xl font-bold mb-6 text-gray-800">Nos services de développement web</h3>
          <ServiceCard />
        </div>

        {/* Tech Stack */}
        <TechStack />

        {/* Pricing Grid */}
        <PricingGrid onAddToPanier={onAddToPanier} />
      </div>
    </section>
  )
}

export default ServicesSection
