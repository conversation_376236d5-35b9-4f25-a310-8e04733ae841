import React, { useState, useEffect } from 'react'
import NetworkAnimation from './NetworkAnimation'

interface Formation {
  id: number
  title: string
  prixPromo: string
  duree: string
  niveau: string
  certification: string
}

interface FormulaireInscriptionFormationProps {
  isOpen: boolean
  onClose: () => void
  formation: Formation | null
}

const FormulaireInscriptionFormation: React.FC<FormulaireInscriptionFormationProps> = ({ 
  isOpen, 
  onClose, 
  formation 
}) => {
  const [isHovering, setIsHovering] = useState(false)
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [showSuccess, setShowSuccess] = useState(false)
  
  const [formData, setFormData] = useState({
    // Informations personnelles
    nom: '',
    prenom: '',
    email: '',
    telephone: '',
    dateNaissance: '',
    profession: '',
    
    // Informations formation
    formationChoisie: '',
    prixFormation: '',
    dureeFormation: '',
    niveauFormation: '',
    
    // Motivations et objectifs
    motivation: '',
    objectifsProfessionnels: '',
    experiencePrealable: '',
    
    // Modalités pratiques
    disponibilite: 'temps-plein',
    modePaiement: 'comptant',
    commentaires: ''
  })

  // Charger les informations de la formation sélectionnée
  useEffect(() => {
    if (formation) {
      setFormData(prev => ({
        ...prev,
        formationChoisie: formation.title,
        prixFormation: formation.prixPromo,
        dureeFormation: formation.duree,
        niveauFormation: formation.niveau
      }))
    }
  }, [formation])

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value } = e.target
    setFormData(prev => ({
      ...prev,
      [name]: value
    }))
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setIsSubmitting(true)

    // Simulation d'envoi
    await new Promise(resolve => setTimeout(resolve, 2000))

    setIsSubmitting(false)
    setShowSuccess(true)

    // Fermer après 3 secondes
    setTimeout(() => {
      setShowSuccess(false)
      onClose()
      // Réinitialiser le formulaire
      setFormData({
        nom: '',
        prenom: '',
        email: '',
        telephone: '',
        dateNaissance: '',
        profession: '',
        formationChoisie: '',
        prixFormation: '',
        dureeFormation: '',
        niveauFormation: '',
        motivation: '',
        objectifsProfessionnels: '',
        experiencePrealable: '',
        disponibilite: 'temps-plein',
        modePaiement: 'comptant',
        commentaires: ''
      })
    }, 3000)
  }

  if (!isOpen) return null

  return (
    <div className="fixed inset-0 bg-white flex items-center justify-center z-50 p-4">
      {/* Animation de réseau en arrière-plan */}
      <NetworkAnimation isExploding={isHovering} />
      
      <div 
        className="bg-white bg-opacity-95 backdrop-blur-sm rounded-2xl max-w-4xl w-full max-h-[90vh] overflow-hidden shadow-2xl border border-gray-200 relative z-10"
        onMouseEnter={() => setIsHovering(true)}
        onMouseLeave={() => setIsHovering(false)}
      >
        {/* En-tête fixe */}
        <div className="bg-gradient-to-r from-green-600 to-blue-600 text-white p-6 flex justify-between items-center">
          <div>
            <h2 className="text-2xl font-bold mb-2 flex items-center">
              <i className="fas fa-graduation-cap mr-3"></i>
              Inscription Formation
              <span className="ml-3 bg-red-500 text-white px-2 py-1 rounded-full text-xs font-bold animate-pulse">
                PROMO !
              </span>
            </h2>
            <p className="text-green-100">
              {formation ? `Inscription à : ${formation.title}` : 'Formulaire d\'inscription aux formations'}
            </p>
          </div>
          <button
            onClick={onClose}
            className="text-white hover:text-gray-200 text-2xl font-bold w-10 h-10 flex items-center justify-center rounded-full hover:bg-white hover:bg-opacity-20 transition-all cursor-pointer"
          >
            ×
          </button>
        </div>

        {/* Animation de succès */}
        {showSuccess && (
          <div className="absolute inset-0 bg-green-600 bg-opacity-95 flex items-center justify-center z-20">
            <div className="text-center text-white">
              <div className="mb-4">
                <i className="fas fa-check-circle text-6xl animate-bounce"></i>
              </div>
              <h3 className="text-2xl font-bold mb-2">Inscription réussie !</h3>
              <p className="text-green-100">
                Votre demande d'inscription a été envoyée avec succès.
                <br />
                Nous vous contacterons sous 24h pour confirmer votre inscription.
              </p>
            </div>
          </div>
        )}

        {/* Contenu scrollable */}
        <div className="overflow-y-auto max-h-[calc(90vh-120px)] p-6">
          <form onSubmit={handleSubmit} className="space-y-6">
            {/* Récapitulatif de la formation */}
            {formation && (
              <div className="bg-gradient-to-r from-green-50 to-blue-50 p-4 rounded-lg border border-green-200">
                <h3 className="text-lg font-bold text-gray-800 mb-3 flex items-center">
                  <i className="fas fa-info-circle text-green-600 mr-2"></i>
                  Formation sélectionnée
                </h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <span className="font-medium text-gray-600">Formation :</span>
                    <span className="ml-2 text-gray-800">{formation.title}</span>
                  </div>
                  <div>
                    <span className="font-medium text-gray-600">Prix promo :</span>
                    <span className="ml-2 text-green-600 font-bold">{formation.prixPromo}</span>
                  </div>
                  <div>
                    <span className="font-medium text-gray-600">Durée :</span>
                    <span className="ml-2 text-gray-800">{formation.duree}</span>
                  </div>
                  <div>
                    <span className="font-medium text-gray-600">Niveau :</span>
                    <span className="ml-2 text-gray-800">{formation.niveau}</span>
                  </div>
                </div>
              </div>
            )}

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {/* Colonne gauche - Informations personnelles */}
              <div>
                <h3 className="text-lg font-bold text-gray-800 mb-4 flex items-center">
                  <i className="fas fa-user text-blue-600 mr-2"></i>
                  Informations personnelles
                </h3>
                
                <div className="space-y-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Nom *
                    </label>
                    <input
                      type="text"
                      name="nom"
                      value={formData.nom}
                      onChange={handleInputChange}
                      required
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent"
                      placeholder="Votre nom de famille"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Prénom *
                    </label>
                    <input
                      type="text"
                      name="prenom"
                      value={formData.prenom}
                      onChange={handleInputChange}
                      required
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent"
                      placeholder="Votre prénom"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Email *
                    </label>
                    <input
                      type="email"
                      name="email"
                      value={formData.email}
                      onChange={handleInputChange}
                      required
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent"
                      placeholder="<EMAIL>"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Téléphone *
                    </label>
                    <input
                      type="tel"
                      name="telephone"
                      value={formData.telephone}
                      onChange={handleInputChange}
                      required
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent"
                      placeholder="+225 XX XX XX XX XX"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Date de naissance
                    </label>
                    <input
                      type="date"
                      name="dateNaissance"
                      value={formData.dateNaissance}
                      onChange={handleInputChange}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Profession actuelle
                    </label>
                    <input
                      type="text"
                      name="profession"
                      value={formData.profession}
                      onChange={handleInputChange}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent"
                      placeholder="Votre profession ou statut"
                    />
                  </div>
                </div>
              </div>

              {/* Colonne droite - Motivations et modalités */}
              <div>
                <h3 className="text-lg font-bold text-gray-800 mb-4 flex items-center">
                  <i className="fas fa-target text-green-600 mr-2"></i>
                  Motivations et modalités
                </h3>
                
                <div className="space-y-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Motivation pour cette formation *
                    </label>
                    <textarea
                      name="motivation"
                      value={formData.motivation}
                      onChange={handleInputChange}
                      required
                      rows={3}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent"
                      placeholder="Pourquoi souhaitez-vous suivre cette formation ?"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Objectifs professionnels
                    </label>
                    <textarea
                      name="objectifsProfessionnels"
                      value={formData.objectifsProfessionnels}
                      onChange={handleInputChange}
                      rows={3}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent"
                      placeholder="Quels sont vos objectifs après cette formation ?"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Expérience préalable
                    </label>
                    <textarea
                      name="experiencePrealable"
                      value={formData.experiencePrealable}
                      onChange={handleInputChange}
                      rows={2}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent"
                      placeholder="Avez-vous déjà une expérience dans ce domaine ?"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Disponibilité *
                    </label>
                    <select
                      name="disponibilite"
                      value={formData.disponibilite}
                      onChange={handleInputChange}
                      required
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent"
                    >
                      <option value="temps-plein">Temps plein</option>
                      <option value="temps-partiel">Temps partiel</option>
                      <option value="weekend">Week-ends uniquement</option>
                      <option value="soir">Cours du soir</option>
                    </select>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Mode de paiement souhaité *
                    </label>
                    <select
                      name="modePaiement"
                      value={formData.modePaiement}
                      onChange={handleInputChange}
                      required
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent"
                    >
                      <option value="comptant">Paiement comptant</option>
                      <option value="2-fois">Paiement en 2 fois</option>
                      <option value="3-fois">Paiement en 3 fois</option>
                      <option value="mensuel">Paiement mensuel</option>
                    </select>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Commentaires ou questions
                    </label>
                    <textarea
                      name="commentaires"
                      value={formData.commentaires}
                      onChange={handleInputChange}
                      rows={3}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent"
                      placeholder="Avez-vous des questions ou des demandes particulières ?"
                    />
                  </div>
                </div>
              </div>
            </div>

            {/* Boutons d'action */}
            <div className="flex gap-4 justify-center pt-6 border-t border-gray-200">
              <button
                type="button"
                onClick={onClose}
                className="px-6 py-3 border border-gray-300 rounded-lg text-gray-700 font-medium hover:bg-gray-50 transition-colors cursor-pointer"
              >
                Annuler
              </button>
              <button
                type="submit"
                disabled={isSubmitting}
                className="px-8 py-3 bg-gradient-to-r from-green-600 to-blue-600 text-white rounded-lg font-medium hover:from-green-700 hover:to-blue-700 transition-all transform hover:scale-105 disabled:opacity-50 disabled:cursor-not-allowed cursor-pointer flex items-center"
              >
                {isSubmitting ? (
                  <>
                    <i className="fas fa-spinner fa-spin mr-2"></i>
                    Inscription en cours...
                  </>
                ) : (
                  <>
                    <i className="fas fa-paper-plane mr-2"></i>
                    Confirmer l'inscription
                  </>
                )}
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  )
}

export default FormulaireInscriptionFormation
