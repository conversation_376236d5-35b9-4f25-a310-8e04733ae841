import React, { useState } from 'react'

const ServiceCard: React.FC = () => {
  const [currentPage, setCurrentPage] = useState(0)

  const allServices = [
    [
      {
        icon: 'fas fa-store-alt',
        title: 'Site Vitrine',
        description: 'Présentez votre entreprise ou activité avec un site web professionnel et attractif.',
        features: [
          'Design personnalisé',
          'Responsive design',
          'Optimisation SEO',
          'Formulaire de contact',
          'Intégration réseaux sociaux'
        ]
      },
      {
        icon: 'fas fa-shopping-cart',
        title: 'Site E-commerce',
        description: 'Vendez vos produits en ligne avec une boutique digitale complète et sécurisée.',
        features: [
          'Catalogue produits',
          'Panier d\'achat',
          'Paiement sécurisé',
          'Gestion des stocks',
          'Suivi des commandes'
        ]
      },
      {
        icon: 'fas fa-cogs',
        title: 'Application Web',
        description: 'Solutions web sur mesure pour répondre à des besoins spécifiques et complexes.',
        features: [
          'Développement sur mesure',
          'Interface utilisateur intuitive',
          'Base de données sécurisée',
          'API et intégrations',
          'Tableau de bord administrateur'
        ]
      }
    ],
    [
      {
        icon: 'fas fa-mobile-alt',
        title: 'Application Mobile',
        description: 'Développement d\'applications mobiles natives et cross-platform pour iOS et Android.',
        features: [
          'Applications natives iOS/Android',
          'Applications cross-platform',
          'Interface utilisateur moderne',
          'Intégration API',
          'Publication sur les stores'
        ]
      },
      {
        icon: 'fas fa-cloud',
        title: 'Solutions Cloud',
        description: 'Migration et déploiement de vos applications vers le cloud pour plus de performance.',
        features: [
          'Migration vers le cloud',
          'Hébergement sécurisé',
          'Sauvegarde automatique',
          'Monitoring 24/7',
          'Scalabilité automatique'
        ]
      },
      {
        icon: 'fas fa-shield-alt',
        title: 'Sécurité Web',
        description: 'Audit et renforcement de la sécurité de vos applications et sites web.',
        features: [
          'Audit de sécurité',
          'Certificats SSL',
          'Protection contre les attaques',
          'Sauvegarde sécurisée',
          'Monitoring de sécurité'
        ]
      }
    ]
  ]

  const currentServices = allServices[currentPage]

  return (
    <div>
      <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
        {currentServices.map((service, index) => (
          <div key={index} className="bg-white rounded-lg shadow-lg overflow-hidden h-full flex flex-col">
            <div className="bg-blue-800 p-6 text-white">
              <div className="flex justify-between items-center mb-4">
                <div className="w-16 h-16 bg-white rounded-full flex items-center justify-center text-blue-800">
                  <i className={`${service.icon} text-2xl`}></i>
                </div>
                <span className="text-sm font-medium bg-blue-700 px-3 py-1 rounded-full">
                  Solution professionnelle
                </span>
              </div>
              <h4 className="text-2xl font-bold mb-2">{service.title}</h4>
              <p className="text-blue-100">{service.description}</p>
            </div>
            <div className="p-6 flex-grow">
              <h5 className="font-bold text-gray-800 mb-4">Fonctionnalités incluses :</h5>
              <ul className="space-y-3 mb-6">
                {service.features.map((feature, featureIndex) => (
                  <li key={featureIndex} className="flex items-start">
                    <i className="fas fa-check-circle text-green-500 mt-1 mr-2"></i>
                    <span>{feature}</span>
                  </li>
                ))}
              </ul>
            </div>
            <div className="p-6 bg-gray-50 border-t border-gray-200">
              <button
                onClick={() => {
                  const contactSection = document.querySelector('[data-section="contact-form"]');
                  if (contactSection) {
                    contactSection.scrollIntoView({ behavior: 'smooth' });
                  }
                }}
                className="w-full bg-blue-800 hover:bg-blue-900 text-white px-4 py-3 rounded-lg font-medium transition-colors rounded-button cursor-pointer whitespace-nowrap"
              >
                Demander un devis
              </button>
            </div>
          </div>
        ))}
      </div>

      <div className="mt-8 text-center">
        <button
          onClick={() => setCurrentPage((prev) => (prev + 1) % allServices.length)}
          className="bg-gradient-to-r from-blue-600 to-purple-600 text-white px-8 py-3 rounded-lg font-semibold hover:from-blue-700 hover:to-purple-700 transition-all duration-300 transform hover:scale-105 shadow-lg cursor-pointer"
        >
          <i className="fas fa-arrow-right mr-2"></i>
          {currentPage === 0 ? 'Voir plus de services' : 'Retour aux services principaux'}
        </button>
      </div>
    </div>
  )
}

export default ServiceCard