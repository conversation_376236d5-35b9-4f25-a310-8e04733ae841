import React, { useState } from 'react'

const FAQ: React.FC = () => {
  const [openFAQ, setOpenFAQ] = useState<number | null>(null)

  const faqs = [
    {
      question: "Quels sont les délais de réalisation pour un site web ?",
      answer: "Les délais varient selon la complexité du projet. Un site vitrine simple peut être réalisé en 2-3 semaines, tandis qu'un e-commerce ou une application web personnalisée peut prendre 1 à 3 mois. Nous établissons un calendrier précis lors de la phase de devis."
    },
    {
      question: "Proposez-vous des formations en groupe ou en entreprise ?",
      answer: "Oui, nous proposons des formations adaptées aux groupes et aux entreprises. Nous pouvons organiser des sessions dans vos locaux ou dans notre centre de formation. Les programmes sont personnalisables selon vos besoins spécifiques."
    },
    {
      question: "Comment se déroule une prestation de conception graphique ?",
      answer: "Le processus commence par une consultation pour comprendre vos besoins. Nous créons ensuite des concepts préliminaires, puis affinons le design choisi avec vos retours. Après validation, nous livrons les fichiers finaux dans tous les formats nécessaires."
    },
    {
      question: "Quelles méthodes de paiement acceptez-vous ?",
      answer: "Nous acceptons les paiements par virement bancaire, mobile money (Orange Money, MTN Mobile Money), et espèces. Pour les projets importants, nous proposons des facilités de paiement en plusieurs tranches."
    },
    {
      question: "Proposez-vous des services de maintenance après la livraison d'un site web ?",
      answer: "Oui, nous offrons des forfaits de maintenance mensuelle ou annuelle qui incluent les mises à jour de sécurité, sauvegardes, corrections de bugs, et modifications mineures. Nous assurons également une assistance technique par email et téléphone."
    }
  ]

  const toggleFAQ = (index: number) => {
    setOpenFAQ(openFAQ === index ? null : index)
  }

  return (
    <section className="py-16 bg-gray-100">
      <div className="container mx-auto px-4">
        <div className="text-center mb-12">
          <h2 className="text-3xl font-bold mb-4">Questions Fréquentes</h2>
          <p className="text-gray-600 max-w-2xl mx-auto">
            Retrouvez les réponses aux questions les plus fréquemment posées sur nos services.
          </p>
        </div>
        <div className="max-w-3xl mx-auto">
          {faqs.map((faq, index) => (
            <div key={index} className="mb-4 bg-white rounded-lg shadow-sm overflow-hidden">
              <div 
                className="p-4 border-b border-gray-100 cursor-pointer hover:bg-gray-50"
                onClick={() => toggleFAQ(index)}
              >
                <div className="flex justify-between items-center">
                  <h3 className="font-bold text-gray-800">{faq.question}</h3>
                  <i className={`fas ${openFAQ === index ? 'fa-chevron-up' : 'fa-chevron-down'} text-gray-500`}></i>
                </div>
              </div>
              {openFAQ === index && (
                <div className="p-4">
                  <p className="text-gray-700">{faq.answer}</p>
                </div>
              )}
            </div>
          ))}
        </div>
      </div>
    </section>
  )
}

export default FAQ
