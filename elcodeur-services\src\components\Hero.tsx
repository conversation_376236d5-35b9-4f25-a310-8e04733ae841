import React, { useState } from 'react'
import AboutModal from './AboutModal'

const Hero: React.FC = () => {
  const [isAboutModalOpen, setIsAboutModalOpen] = useState(false)

  return (
    <div className="relative overflow-hidden">
      <div className="absolute inset-0 z-0">
        <img 
          src="/search-image" 
          alt="Services professionnels" 
          className="w-full h-full object-cover object-top"
        />
        <div className="absolute inset-0 bg-gradient-to-r from-blue-900/90 to-blue-900/70"></div>
      </div>
      <div className="container mx-auto px-4 py-16 relative z-10">
        <div className="flex items-center justify-between">
          {/* Contenu principal à gauche */}
          <div className="flex-1 max-w-3xl">
            <a href="#" className="inline-flex items-center text-white mb-4 hover:text-blue-200 transition-colors no-underline">
              <i className="fas fa-arrow-left mr-2"></i> Retour à l'accueil
            </a>
            <div className="text-white">
              <h1 className="text-4xl md:text-5xl font-bold mb-4">Nos Services</h1>
              <p className="text-xl mb-6">
                Découvrez notre gamme complète de services numériques professionnels,
                conçus pour répondre à tous vos besoins et propulser votre présence digitale.
              </p>

              {/* Bouton Plus-Infos */}
              <button
                onClick={() => setIsAboutModalOpen(true)}
                className="border-2 border-red-800 text-white px-6 py-3 rounded-lg font-medium transition-all hover:border-white hover:bg-red-800 cursor-pointer"
              >
                Plus-Infos
              </button>

              {/* Logo mobile - visible uniquement sur petits écrans */}
              <div className="md:hidden flex justify-center mt-8">
                <div className="hero-logo-circle w-28 h-28 bg-white rounded-full flex items-center justify-center">
                  <img
                    src="/logo.gif"
                    alt="elCodeur Services Logo"
                    className="w-28 h-28 object-contain"
                  />
                </div>
              </div>
            </div>
          </div>

          {/* Logo dans un cercle blanc à droite */}
          <div className="hidden md:flex items-start justify-end ml-16 -mt-16 mr-4">
            <div className="hero-logo-circle w-32 h-32 lg:w-36 lg:h-36 bg-white rounded-full flex items-center justify-center shadow-2xl">
              <img
                src="/logo.gif"
                alt="elCodeur Services Logo"
                className="w-32 h-32 lg:w-36 lg:h-36 object-contain"
              />
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}

export default Hero
