import React, { useState } from 'react'
import AboutModal from './AboutModal'
import ListeServices from './ListeServices'
import ListeFormations from './ListeFormations'

const Header: React.FC = () => {
  const [isAboutModalOpen, setIsAboutModalOpen] = useState(false)
  const [isServicesModalOpen, setIsServicesModalOpen] = useState(false)
  const [isFormationsModalOpen, setIsFormationsModalOpen] = useState(false)
  return (
    <header className="bg-white shadow-md">
      <div className="container-fluid mx-auto px-4 py-4 flex justify-between items-center">
        <div className="flex items-center">
          <div className="flex items-center">
            <span className="text-3xl font-bold">
              <span className="text-blue-800">e</span>
              <span className="text-red-600">L</span>
              <span className="text-blue-800">S</span>
            </span>
            <span className="ml-2 text-gray-700 font-serif italic">elCodeur Services.</span>
          </div>
        </div>
        <nav className="hidden md:flex space-x-8 items-center">
          <a href="#" className="font-medium text-blue-800 rounded-button cursor-pointer whitespace-nowrap no-underline">
            Accueil
          </a>
          <button
            onClick={() => setIsAboutModalOpen(true)}
            className="font-medium text-gray-600 hover:text-blue-800 rounded-button cursor-pointer whitespace-nowrap"
          >
            Qui-Sommes-Nous ?
          </button>
          <button
            onClick={() => setIsServicesModalOpen(true)}
            className="font-medium text-gray-600 hover:text-blue-800 rounded-button cursor-pointer whitespace-nowrap"
          >
            Nos Services
          </button>
          <button
            onClick={() => setIsFormationsModalOpen(true)}
            className="font-medium text-gray-600 hover:text-blue-800 rounded-button cursor-pointer whitespace-nowrap"
          >
            Formation <small><i>en Promo</i></small>
          </button>
          <button className="font-medium text-gray-600 hover:text-blue-800 rounded-button cursor-pointer whitespace-nowrap">
            Contact
          </button>

          {/* Icône scroll vers footer avec animation */}
          <button
            onClick={() => {
              const footer = document.querySelector('footer');
              if (footer) {
                footer.scrollIntoView({ behavior: 'smooth' });
              }
            }}
            className="ml-12 text-blue-600 hover:text-blue-800 transition-colors duration-300 cursor-pointer"
            title="Aller au footer"
          >
            <i className="fas fa-angle-double-down text-xl bounce-animation"></i>
          </button>
        </nav>
        <button className="md:hidden text-gray-600">
          <i className="fas fa-bars text-xl"></i>
        </button>
      </div>

      {/* Modale About */}
      <AboutModal
        isOpen={isAboutModalOpen}
        onClose={() => setIsAboutModalOpen(false)}
      />

      {/* Modale Services */}
      <ListeServices
        isOpen={isServicesModalOpen}
        onClose={() => setIsServicesModalOpen(false)}
      />

      {/* Modale Formations */}
      <ListeFormations
        isOpen={isFormationsModalOpen}
        onClose={() => setIsFormationsModalOpen(false)}
      />
    </header>
  )
}

export default Header
