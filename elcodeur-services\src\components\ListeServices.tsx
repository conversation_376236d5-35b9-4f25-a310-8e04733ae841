import React, { useState } from 'react'

interface Service {
  id: number
  icon: string
  title: string
  description: string
  features: string[]
  category: string
  prix: string
  duree: string
  technologies: string[]
  processus: string[]
  avantages: string[]
}

interface ListeServicesProps {
  isOpen: boolean
  onClose: () => void
}

const ListeServices: React.FC<ListeServicesProps> = ({ isOpen, onClose }) => {
  const [selectedCategory, setSelectedCategory] = useState<string>('Tous')
  const [selectedService, setSelectedService] = useState<Service | null>(null)

  const services: Service[] = [
    {
      id: 1,
      icon: 'fas fa-store-alt',
      title: 'Site Vitrine',
      description: 'Présentez votre entreprise ou activité avec un site web professionnel et attractif.',
      features: [
        'Design personnalisé',
        'Responsive design',
        'Optimisation SEO',
        'Formulaire de contact',
        'Intégration réseaux sociaux'
      ],
      category: 'Web',
      prix: '250 000 FCFA',
      duree: '2-3 semaines',
      technologies: ['HTML5', 'CSS3', 'JavaScript', 'React', 'Tailwind CSS'],
      processus: [
        'Analyse des besoins et cahier des charges',
        'Création des maquettes et design',
        'Développement et intégration',
        'Tests et optimisation',
        'Mise en ligne et formation'
      ],
      avantages: [
        'Présence professionnelle en ligne',
        'Visibilité 24h/24 et 7j/7',
        'Crédibilité renforcée',
        'Génération de leads qualifiés',
        'Référencement naturel optimisé'
      ]
    },
    {
      id: 2,
      icon: 'fas fa-shopping-cart',
      title: 'Site E-commerce',
      description: 'Vendez vos produits en ligne avec une boutique digitale complète et sécurisée.',
      features: [
        'Catalogue produits',
        'Panier d\'achat',
        'Paiement sécurisé',
        'Gestion des stocks',
        'Suivi des commandes'
      ],
      category: 'E-commerce',
      prix: '450 000 FCFA',
      duree: '4-6 semaines',
      technologies: ['React', 'Node.js', 'MongoDB', 'Stripe', 'PayPal'],
      processus: [
        'Étude du marché et concurrence',
        'Architecture de la boutique',
        'Développement des fonctionnalités',
        'Intégration des paiements',
        'Tests et mise en production'
      ],
      avantages: [
        'Vente en ligne 24h/24',
        'Gestion automatisée des commandes',
        'Paiements sécurisés',
        'Suivi des performances',
        'Expansion du marché'
      ]
    },
    {
      id: 3,
      icon: 'fas fa-cogs',
      title: 'Application Web',
      description: 'Solutions web sur mesure pour répondre à des besoins spécifiques et complexes.',
      features: [
        'Développement sur mesure',
        'Interface utilisateur intuitive',
        'Base de données sécurisée',
        'API et intégrations',
        'Tableau de bord administrateur'
      ],
      category: 'Web',
      prix: '800 000 FCFA',
      duree: '6-10 semaines',
      technologies: ['React', 'Node.js', 'PostgreSQL', 'Redis', 'Docker'],
      processus: [
        'Analyse approfondie des besoins',
        'Conception de l\'architecture',
        'Développement par modules',
        'Tests unitaires et d\'intégration',
        'Déploiement et maintenance'
      ],
      avantages: [
        'Solution parfaitement adaptée',
        'Évolutivité et scalabilité',
        'Performance optimisée',
        'Sécurité renforcée',
        'Support technique dédié'
      ]
    },
    {
      id: 4,
      icon: 'fas fa-mobile-alt',
      title: 'Application Mobile',
      description: 'Développement d\'applications mobiles natives et cross-platform pour iOS et Android.',
      features: [
        'Applications natives iOS/Android',
        'Applications cross-platform',
        'Interface utilisateur moderne',
        'Intégration API',
        'Publication sur les stores'
      ],
      category: 'Mobile',
      prix: '600 000 FCFA',
      duree: '8-12 semaines',
      technologies: ['React Native', 'Flutter', 'Swift', 'Kotlin', 'Firebase'],
      processus: [
        'Définition des spécifications',
        'Design UX/UI mobile',
        'Développement natif ou cross-platform',
        'Tests sur différents appareils',
        'Publication et suivi'
      ],
      avantages: [
        'Accessibilité mobile optimale',
        'Expérience utilisateur native',
        'Notifications push',
        'Fonctionnalités hors ligne',
        'Monétisation possible'
      ]
    },
    {
      id: 5,
      icon: 'fas fa-cloud',
      title: 'Solutions Cloud',
      description: 'Migration et déploiement de vos applications vers le cloud pour plus de performance.',
      features: [
        'Migration vers le cloud',
        'Hébergement sécurisé',
        'Sauvegarde automatique',
        'Monitoring 24/7',
        'Scalabilité automatique'
      ],
      category: 'Infrastructure',
      prix: '350 000 FCFA',
      duree: '3-4 semaines',
      technologies: ['AWS', 'Azure', 'Google Cloud', 'Docker', 'Kubernetes'],
      processus: [
        'Audit de l\'infrastructure existante',
        'Planification de la migration',
        'Migration progressive',
        'Configuration du monitoring',
        'Optimisation et formation'
      ],
      avantages: [
        'Réduction des coûts IT',
        'Haute disponibilité',
        'Sécurité renforcée',
        'Évolutivité instantanée',
        'Maintenance simplifiée'
      ]
    },
    {
      id: 6,
      icon: 'fas fa-shield-alt',
      title: 'Sécurité Web',
      description: 'Audit et renforcement de la sécurité de vos applications et sites web.',
      features: [
        'Audit de sécurité',
        'Certificats SSL',
        'Protection contre les attaques',
        'Sauvegarde sécurisée',
        'Monitoring de sécurité'
      ],
      category: 'Sécurité',
      prix: '200 000 FCFA',
      duree: '1-2 semaines',
      technologies: ['SSL/TLS', 'WAF', 'OWASP', 'Penetration Testing', 'SIEM'],
      processus: [
        'Audit de sécurité complet',
        'Identification des vulnérabilités',
        'Mise en place des protections',
        'Tests de pénétration',
        'Rapport et recommandations'
      ],
      avantages: [
        'Protection contre les cyberattaques',
        'Conformité réglementaire',
        'Confiance des utilisateurs',
        'Continuité d\'activité',
        'Réputation préservée'
      ]
    }
  ]

  const categories = ['Tous', 'Web', 'E-commerce', 'Mobile', 'Infrastructure', 'Sécurité']

  const filteredServices = selectedCategory === 'Tous' 
    ? services 
    : services.filter(service => service.category === selectedCategory)

  if (!isOpen) return null

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-2xl max-w-7xl w-full max-h-[90vh] overflow-hidden shadow-2xl">
        {/* En-tête fixe */}
        <div className="bg-gradient-to-r from-blue-600 to-purple-600 text-white p-6 flex justify-between items-center">
          <div>
            <h2 className="text-3xl font-bold mb-2">Nos Services</h2>
            <p className="text-blue-100">Découvrez notre gamme complète de services digitaux</p>
          </div>
          <button
            onClick={onClose}
            className="text-white hover:text-gray-200 text-2xl font-bold w-10 h-10 flex items-center justify-center rounded-full hover:bg-white hover:bg-opacity-20 transition-all cursor-pointer"
          >
            ×
          </button>
        </div>

        {/* Filtres par catégorie */}
        <div className="p-6 border-b border-gray-200">
          <div className="flex flex-wrap gap-2">
            {categories.map((category) => (
              <button
                key={category}
                onClick={() => setSelectedCategory(category)}
                className={`px-4 py-2 rounded-full text-sm font-medium transition-all cursor-pointer ${
                  selectedCategory === category
                    ? 'bg-blue-600 text-white'
                    : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                }`}
              >
                {category}
              </button>
            ))}
          </div>
        </div>

        {/* Contenu scrollable */}
        <div className="overflow-y-auto max-h-[calc(90vh-200px)] p-6">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {filteredServices.map((service) => (
              <div key={service.id} className="bg-white border border-gray-200 rounded-xl overflow-hidden hover:shadow-lg transition-all duration-300">
                <div className="bg-gradient-to-r from-blue-600 to-purple-600 text-white p-4">
                  <div className="flex items-center justify-between mb-3">
                    <div className="w-12 h-12 bg-white bg-opacity-20 rounded-full flex items-center justify-center">
                      <i className={`${service.icon} text-xl`}></i>
                    </div>
                    <span className="text-sm bg-white bg-opacity-20 px-2 py-1 rounded-full">
                      {service.category}
                    </span>
                  </div>
                  <h3 className="text-xl font-bold mb-2">{service.title}</h3>
                  <p className="text-blue-100 text-sm">{service.description}</p>
                </div>

                <div className="p-4">
                  <div className="mb-4">
                    <div className="flex justify-between items-center mb-2">
                      <span className="text-sm font-medium text-gray-600">Prix</span>
                      <span className="text-lg font-bold text-blue-600">{service.prix}</span>
                    </div>
                    <div className="flex justify-between items-center">
                      <span className="text-sm font-medium text-gray-600">Durée</span>
                      <span className="text-sm text-gray-800">{service.duree}</span>
                    </div>
                  </div>

                  <div className="mb-4">
                    <h4 className="font-semibold text-gray-800 mb-2">Fonctionnalités :</h4>
                    <ul className="space-y-1">
                      {service.features.slice(0, 3).map((feature, index) => (
                        <li key={index} className="flex items-start text-sm">
                          <i className="fas fa-check text-green-500 mt-1 mr-2 text-xs"></i>
                          <span className="text-gray-600">{feature}</span>
                        </li>
                      ))}
                      {service.features.length > 3 && (
                        <li className="text-sm text-blue-600">
                          +{service.features.length - 3} autres fonctionnalités
                        </li>
                      )}
                    </ul>
                  </div>

                  <div className="flex gap-2">
                    <button
                      onClick={() => {
                        const contactSection = document.querySelector('[data-section="contact-form"]');
                        if (contactSection) {
                          contactSection.scrollIntoView({ behavior: 'smooth' });
                          onClose();
                        }
                      }}
                      className="flex-1 bg-blue-600 hover:bg-blue-700 text-white px-3 py-2 rounded-lg text-sm font-medium transition-colors cursor-pointer"
                    >
                      Demander un devis
                    </button>
                    <button
                      onClick={() => setSelectedService(service)}
                      className="px-3 py-2 border border-gray-300 rounded-lg text-sm font-medium text-gray-700 hover:bg-gray-50 transition-colors cursor-pointer"
                    >
                      Détails
                    </button>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* Modale de détails du service */}
      {selectedService && (
        <div className="fixed inset-0 bg-black bg-opacity-75 flex items-center justify-center z-60 p-4">
          <div className="bg-white rounded-2xl max-w-4xl w-full max-h-[90vh] overflow-hidden shadow-2xl">
            <div className="bg-gradient-to-r from-blue-600 to-purple-600 text-white p-6 flex justify-between items-center">
              <div>
                <h3 className="text-2xl font-bold mb-2">{selectedService.title}</h3>
                <p className="text-blue-100">{selectedService.description}</p>
              </div>
              <button
                onClick={() => setSelectedService(null)}
                className="text-white hover:text-gray-200 text-2xl font-bold w-10 h-10 flex items-center justify-center rounded-full hover:bg-white hover:bg-opacity-20 transition-all cursor-pointer"
              >
                ×
              </button>
            </div>

            <div className="overflow-y-auto max-h-[calc(90vh-120px)] p-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <div className="mb-6">
                    <h4 className="text-lg font-bold text-gray-800 mb-3">Informations générales</h4>
                    <div className="space-y-2">
                      <div className="flex justify-between">
                        <span className="font-medium text-gray-600">Prix :</span>
                        <span className="text-blue-600 font-bold">{selectedService.prix}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="font-medium text-gray-600">Durée :</span>
                        <span className="text-gray-800">{selectedService.duree}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="font-medium text-gray-600">Catégorie :</span>
                        <span className="text-gray-800">{selectedService.category}</span>
                      </div>
                    </div>
                  </div>

                  <div className="mb-6">
                    <h4 className="text-lg font-bold text-gray-800 mb-3">Technologies utilisées</h4>
                    <div className="flex flex-wrap gap-2">
                      {selectedService.technologies.map((tech, index) => (
                        <span key={index} className="bg-blue-100 text-blue-800 px-3 py-1 rounded-full text-sm font-medium">
                          {tech}
                        </span>
                      ))}
                    </div>
                  </div>

                  <div>
                    <h4 className="text-lg font-bold text-gray-800 mb-3">Processus de développement</h4>
                    <ul className="space-y-2">
                      {selectedService.processus.map((etape, index) => (
                        <li key={index} className="flex items-start">
                          <span className="bg-blue-600 text-white rounded-full w-6 h-6 flex items-center justify-center text-sm font-bold mr-3 mt-0.5">
                            {index + 1}
                          </span>
                          <span className="text-gray-700">{etape}</span>
                        </li>
                      ))}
                    </ul>
                  </div>
                </div>

                <div>
                  <div className="mb-6">
                    <h4 className="text-lg font-bold text-gray-800 mb-3">Fonctionnalités complètes</h4>
                    <ul className="space-y-2">
                      {selectedService.features.map((feature, index) => (
                        <li key={index} className="flex items-start">
                          <i className="fas fa-check-circle text-green-500 mt-1 mr-2"></i>
                          <span className="text-gray-700">{feature}</span>
                        </li>
                      ))}
                    </ul>
                  </div>

                  <div>
                    <h4 className="text-lg font-bold text-gray-800 mb-3">Avantages pour votre entreprise</h4>
                    <ul className="space-y-2">
                      {selectedService.avantages.map((avantage, index) => (
                        <li key={index} className="flex items-start">
                          <i className="fas fa-star text-yellow-500 mt-1 mr-2"></i>
                          <span className="text-gray-700">{avantage}</span>
                        </li>
                      ))}
                    </ul>
                  </div>
                </div>
              </div>

              <div className="mt-8 flex gap-4 justify-center">
                <button
                  onClick={() => {
                    const contactSection = document.querySelector('[data-section="contact-form"]');
                    if (contactSection) {
                      contactSection.scrollIntoView({ behavior: 'smooth' });
                      setSelectedService(null);
                      onClose();
                    }
                  }}
                  className="bg-blue-600 hover:bg-blue-700 text-white px-6 py-3 rounded-lg font-medium transition-colors cursor-pointer"
                >
                  <i className="fas fa-envelope mr-2"></i>
                  Demander un devis
                </button>
                <button
                  onClick={() => setSelectedService(null)}
                  className="bg-gray-500 hover:bg-gray-600 text-white px-6 py-3 rounded-lg font-medium transition-colors cursor-pointer"
                >
                  Fermer
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}

export default ListeServices
