import React, { useState } from 'react'
import NetworkAnimation from './NetworkAnimation'

interface FormulaireContactProps {
  isOpen: boolean
  onClose: () => void
}

const FormulaireContact: React.FC<FormulaireContactProps> = ({ isOpen, onClose }) => {
  const [isHovering, setIsHovering] = useState(false)
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [showSuccess, setShowSuccess] = useState(false)
  
  const [formData, setFormData] = useState({
    nom: '',
    prenom: '',
    email: '',
    telephone: '',
    entreprise: '',
    sujet: '',
    typeService: '',
    budget: '',
    delai: '',
    message: '',
    newsletter: false
  })

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value, type } = e.target
    if (type === 'checkbox') {
      const checked = (e.target as HTMLInputElement).checked
      setFormData(prev => ({
        ...prev,
        [name]: checked
      }))
    } else {
      setFormData(prev => ({
        ...prev,
        [name]: value
      }))
    }
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setIsSubmitting(true)

    // Simulation d'envoi
    await new Promise(resolve => setTimeout(resolve, 2000))

    setIsSubmitting(false)
    setShowSuccess(true)

    // Fermer après 3 secondes
    setTimeout(() => {
      setShowSuccess(false)
      onClose()
      // Réinitialiser le formulaire
      setFormData({
        nom: '',
        prenom: '',
        email: '',
        telephone: '',
        entreprise: '',
        sujet: '',
        typeService: '',
        budget: '',
        delai: '',
        message: '',
        newsletter: false
      })
    }, 3000)
  }

  if (!isOpen) return null

  return (
    <div className="fixed inset-0 bg-white flex items-center justify-center z-50 p-4">
      {/* Animation de réseau en arrière-plan */}
      <NetworkAnimation isExploding={isHovering} />
      
      <div 
        className="bg-white bg-opacity-95 backdrop-blur-sm rounded-2xl max-w-5xl w-full max-h-[90vh] overflow-hidden shadow-2xl border border-gray-200 relative z-10"
        onMouseEnter={() => setIsHovering(true)}
        onMouseLeave={() => setIsHovering(false)}
      >
        {/* En-tête fixe */}
        <div className="bg-gradient-to-r from-blue-600 to-purple-600 text-white p-6 flex justify-between items-center">
          <div>
            <h2 className="text-3xl font-bold mb-2 flex items-center">
              <i className="fas fa-envelope mr-3"></i>
              Contactez-nous
            </h2>
            <p className="text-blue-100">Parlez-nous de votre projet, nous vous répondrons rapidement</p>
          </div>
          <button
            onClick={onClose}
            className="text-white hover:text-gray-200 text-2xl font-bold w-10 h-10 flex items-center justify-center rounded-full hover:bg-white hover:bg-opacity-20 transition-all cursor-pointer"
          >
            ×
          </button>
        </div>

        {/* Animation de succès */}
        {showSuccess && (
          <div className="absolute inset-0 bg-blue-600 bg-opacity-95 flex items-center justify-center z-20">
            <div className="text-center text-white">
              <div className="mb-4">
                <i className="fas fa-check-circle text-6xl animate-bounce"></i>
              </div>
              <h3 className="text-2xl font-bold mb-2">Message envoyé !</h3>
              <p className="text-blue-100">
                Votre message a été envoyé avec succès.
                <br />
                Nous vous répondrons dans les plus brefs délais.
              </p>
            </div>
          </div>
        )}

        {/* Contenu scrollable */}
        <div className="overflow-y-auto max-h-[calc(90vh-120px)] p-6">
          <form onSubmit={handleSubmit} className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {/* Colonne gauche - Informations personnelles */}
              <div>
                <h3 className="text-lg font-bold text-gray-800 mb-4 flex items-center">
                  <i className="fas fa-user text-blue-600 mr-2"></i>
                  Vos informations
                </h3>
                
                <div className="space-y-4">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        Nom *
                      </label>
                      <input
                        type="text"
                        name="nom"
                        value={formData.nom}
                        onChange={handleInputChange}
                        required
                        className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                        placeholder="Votre nom"
                      />
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        Prénom *
                      </label>
                      <input
                        type="text"
                        name="prenom"
                        value={formData.prenom}
                        onChange={handleInputChange}
                        required
                        className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                        placeholder="Votre prénom"
                      />
                    </div>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Email *
                    </label>
                    <input
                      type="email"
                      name="email"
                      value={formData.email}
                      onChange={handleInputChange}
                      required
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                      placeholder="<EMAIL>"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Téléphone
                    </label>
                    <input
                      type="tel"
                      name="telephone"
                      value={formData.telephone}
                      onChange={handleInputChange}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                      placeholder="+225 XX XX XX XX XX"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Entreprise / Organisation
                    </label>
                    <input
                      type="text"
                      name="entreprise"
                      value={formData.entreprise}
                      onChange={handleInputChange}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                      placeholder="Nom de votre entreprise"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Sujet *
                    </label>
                    <input
                      type="text"
                      name="sujet"
                      value={formData.sujet}
                      onChange={handleInputChange}
                      required
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                      placeholder="Objet de votre demande"
                    />
                  </div>
                </div>
              </div>

              {/* Colonne droite - Détails du projet */}
              <div>
                <h3 className="text-lg font-bold text-gray-800 mb-4 flex items-center">
                  <i className="fas fa-project-diagram text-purple-600 mr-2"></i>
                  Votre projet
                </h3>
                
                <div className="space-y-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Type de service souhaité
                    </label>
                    <select
                      name="typeService"
                      value={formData.typeService}
                      onChange={handleInputChange}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    >
                      <option value="">Sélectionnez un service</option>
                      <option value="site-vitrine">Site Vitrine</option>
                      <option value="site-ecommerce">Site E-commerce</option>
                      <option value="application-web">Application Web</option>
                      <option value="application-mobile">Application Mobile</option>
                      <option value="solutions-cloud">Solutions Cloud</option>
                      <option value="securite-web">Sécurité Web</option>
                      <option value="formation">Formation</option>
                      <option value="autre">Autre</option>
                    </select>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Budget approximatif
                    </label>
                    <select
                      name="budget"
                      value={formData.budget}
                      onChange={handleInputChange}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    >
                      <option value="">Sélectionnez votre budget</option>
                      <option value="moins-100k">Moins de 100 000 FCFA</option>
                      <option value="100k-300k">100 000 - 300 000 FCFA</option>
                      <option value="300k-500k">300 000 - 500 000 FCFA</option>
                      <option value="500k-1m">500 000 FCFA - 1 000 000 FCFA</option>
                      <option value="plus-1m">Plus de 1 000 000 FCFA</option>
                      <option value="a-discuter">À discuter</option>
                    </select>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Délai souhaité
                    </label>
                    <select
                      name="delai"
                      value={formData.delai}
                      onChange={handleInputChange}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    >
                      <option value="">Sélectionnez un délai</option>
                      <option value="urgent">Urgent (moins de 2 semaines)</option>
                      <option value="1-mois">Dans le mois</option>
                      <option value="2-3-mois">2-3 mois</option>
                      <option value="plus-3-mois">Plus de 3 mois</option>
                      <option value="flexible">Flexible</option>
                    </select>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Décrivez votre projet *
                    </label>
                    <textarea
                      name="message"
                      value={formData.message}
                      onChange={handleInputChange}
                      required
                      rows={6}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                      placeholder="Décrivez votre projet, vos besoins, vos objectifs..."
                    />
                  </div>

                  <div className="flex items-center">
                    <input
                      type="checkbox"
                      name="newsletter"
                      checked={formData.newsletter}
                      onChange={handleInputChange}
                      className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                    />
                    <label className="ml-2 block text-sm text-gray-700">
                      Je souhaite recevoir la newsletter avec les dernières actualités et offres
                    </label>
                  </div>
                </div>
              </div>
            </div>

            {/* Informations de contact */}
            <div className="bg-gradient-to-r from-blue-50 to-purple-50 p-4 rounded-lg border border-blue-200">
              <h3 className="text-lg font-bold text-gray-800 mb-3 flex items-center">
                <i className="fas fa-info-circle text-blue-600 mr-2"></i>
                Nos coordonnées
              </h3>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
                <div className="flex items-center">
                  <i className="fas fa-phone text-blue-600 mr-2"></i>
                  <span>+225 05 56 75 04 67</span>
                </div>
                <div className="flex items-center">
                  <i className="fas fa-envelope text-blue-600 mr-2"></i>
                  <span><EMAIL></span>
                </div>
                <div className="flex items-center">
                  <i className="fas fa-clock text-blue-600 mr-2"></i>
                  <span>Réponse sous 24h</span>
                </div>
              </div>
            </div>

            {/* Boutons d'action */}
            <div className="flex gap-4 justify-center pt-6 border-t border-gray-200">
              <button
                type="button"
                onClick={onClose}
                className="px-6 py-3 border border-gray-300 rounded-lg text-gray-700 font-medium hover:bg-gray-50 transition-colors cursor-pointer"
              >
                Annuler
              </button>
              <button
                type="submit"
                disabled={isSubmitting}
                className="px-8 py-3 bg-gradient-to-r from-blue-600 to-purple-600 text-white rounded-lg font-medium hover:from-blue-700 hover:to-purple-700 transition-all transform hover:scale-105 disabled:opacity-50 disabled:cursor-not-allowed cursor-pointer flex items-center"
              >
                {isSubmitting ? (
                  <>
                    <i className="fas fa-spinner fa-spin mr-2"></i>
                    Envoi en cours...
                  </>
                ) : (
                  <>
                    <i className="fas fa-paper-plane mr-2"></i>
                    Envoyer le message
                  </>
                )}
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  )
}

export default FormulaireContact
