import React, { useState, useEffect } from 'react'
import PanierModal from './PanierModal'

interface PanierItem {
  id: string
  title: string
  price: number
  quantity: number
  description: string
}

interface PanierProps {
  items: PanierItem[]
  onUpdateQuantity: (id: string, quantity: number) => void
  onRemoveItem: (id: string) => void
  onClearPanier: () => void
}

const Panier: React.FC<PanierProps> = ({ items, onUpdateQuantity, onRemoveItem, onClearPanier }) => {
  const [isOpen, setIsOpen] = useState(false)
  const [isModalOpen, setIsModalOpen] = useState(false)
  const [animate, setAnimate] = useState(false)

  // Animation quand un item est ajouté
  useEffect(() => {
    if (items.length > 0) {
      setAnimate(true)
      const timer = setTimeout(() => setAnimate(false), 500)
      return () => clearTimeout(timer)
    }
  }, [items.length])

  // Calcul du total
  const totalAmount = items.reduce((total, item) => total + (item.price * item.quantity), 0)
  const totalItems = items.reduce((total, item) => total + item.quantity, 0)

  // Format du prix en FCFA
  const formatPrice = (price: number) => {
    return new Intl.NumberFormat('fr-FR').format(price) + ' FCFA'
  }

  return (
    <>
      {/* Panier fixé à droite */}
      <div className={`fixed right-4 top-1/2 transform -translate-y-1/2 z-40 transition-all duration-300 ${animate ? 'scale-110' : 'scale-100'}`}>
        <div className="bg-white rounded-2xl shadow-2xl border border-gray-200 overflow-hidden max-w-sm">
          {/* En-tête du panier */}
          <div 
            className="bg-gradient-to-r from-blue-800 to-blue-900 text-white p-4 cursor-pointer hover:from-blue-900 hover:to-blue-800 transition-all duration-300"
            onClick={() => setIsOpen(!isOpen)}
          >
            <div className="flex items-center justify-between">
              <div className="flex items-center">
                <div className="relative">
                  <i className="fas fa-shopping-cart text-xl"></i>
                  {totalItems > 0 && (
                    <span className="absolute -top-2 -right-2 bg-red-500 text-white text-xs rounded-full w-5 h-5 flex items-center justify-center font-bold">
                      {totalItems}
                    </span>
                  )}
                </div>
                <span className="ml-3 font-bold">Panier</span>
              </div>
              <i className={`fas fa-chevron-${isOpen ? 'up' : 'down'} transition-transform duration-300`}></i>
            </div>
            {totalAmount > 0 && (
              <div className="mt-2 text-sm text-blue-100">
                Total: {formatPrice(totalAmount)}
              </div>
            )}
          </div>

          {/* Contenu du panier (collapsible) */}
          <div className={`transition-all duration-300 overflow-hidden ${isOpen ? 'max-h-96' : 'max-h-0'}`}>
            {items.length === 0 ? (
              <div className="p-4 text-center text-gray-500">
                <i className="fas fa-shopping-cart text-3xl mb-2 text-gray-300"></i>
                <p>Votre panier est vide</p>
              </div>
            ) : (
              <div className="max-h-80 overflow-y-auto">
                {items.map((item) => (
                  <div key={item.id} className="p-3 border-b border-gray-100 hover:bg-gray-50 transition-colors">
                    <div className="flex justify-between items-start mb-2">
                      <h4 className="font-medium text-sm text-gray-800 flex-1">{item.title}</h4>
                      <button
                        onClick={() => onRemoveItem(item.id)}
                        className="text-red-500 hover:text-red-700 ml-2 cursor-pointer"
                        title="Supprimer"
                      >
                        <i className="fas fa-times text-xs"></i>
                      </button>
                    </div>
                    <div className="flex justify-between items-center">
                      <div className="flex items-center">
                        <button
                          onClick={() => onUpdateQuantity(item.id, Math.max(1, item.quantity - 1))}
                          className="w-6 h-6 bg-gray-200 hover:bg-gray-300 rounded-full flex items-center justify-center text-xs cursor-pointer"
                        >
                          -
                        </button>
                        <span className="mx-2 text-sm font-medium">{item.quantity}</span>
                        <button
                          onClick={() => onUpdateQuantity(item.id, item.quantity + 1)}
                          className="w-6 h-6 bg-gray-200 hover:bg-gray-300 rounded-full flex items-center justify-center text-xs cursor-pointer"
                        >
                          +
                        </button>
                      </div>
                      <span className="text-sm font-bold text-blue-800">
                        {formatPrice(item.price * item.quantity)}
                      </span>
                    </div>
                  </div>
                ))}
              </div>
            )}

            {/* Actions du panier */}
            {items.length > 0 && (
              <div className="p-4 bg-gray-50 border-t border-gray-200">
                <div className="flex justify-between items-center mb-3">
                  <span className="font-bold text-gray-800">Total:</span>
                  <span className="font-bold text-lg text-blue-800">{formatPrice(totalAmount)}</span>
                </div>
                <div className="flex gap-2">
                  <button
                    onClick={() => setIsModalOpen(true)}
                    className="flex-1 bg-gradient-to-r from-green-600 to-green-700 text-white py-2 px-3 rounded-lg font-medium hover:from-green-700 hover:to-green-800 transition-all duration-300 text-sm cursor-pointer"
                  >
                    <i className="fas fa-credit-card mr-1"></i>
                    Commander
                  </button>
                  <button
                    onClick={onClearPanier}
                    className="bg-red-500 hover:bg-red-600 text-white py-2 px-3 rounded-lg font-medium transition-all duration-300 text-sm cursor-pointer"
                    title="Vider le panier"
                  >
                    <i className="fas fa-trash"></i>
                  </button>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Modale de commande */}
      <PanierModal
        isOpen={isModalOpen}
        onClose={() => setIsModalOpen(false)}
        items={items}
        totalAmount={totalAmount}
        onClearPanier={onClearPanier}
      />
    </>
  )
}

export default Panier
