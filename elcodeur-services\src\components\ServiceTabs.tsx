import React, { useState } from 'react'
import ModaleTemoignages from './ModaleTemoignages'
import ModalePartenaires from './ModalePartenaires'
import ModaleEquipes from './ModaleEquipes'
import ModaleFormateurs from './ModaleFormateurs'

const ServiceTabs: React.FC = () => {
  const [activeTab, setActiveTab] = useState('equipes')
  const [isTemoignagesOpen, setIsTemoignagesOpen] = useState(false)
  const [isPartenairesOpen, setIsPartenairesOpen] = useState(false)
  const [isEquipesOpen, setIsEquipesOpen] = useState(false)
  const [isFormateursOpen, setIsFormateursOpen] = useState(false)

  const tabs = [
    { id: 'temoignages', label: 'Nos Témoignages', icon: 'fas fa-quote-left', action: () => setIsTemoignagesOpen(true) },
    { id: 'partenaires', label: 'Nos Partenaires', icon: 'fas fa-handshake', action: () => setIsPartenairesOpen(true) },
    { id: 'equipes', label: 'Nos Équipes', icon: 'fas fa-users', action: () => setIsEquipesOpen(true) },
    { id: 'formateurs', label: 'Nos Formateurs', icon: 'fas fa-chalkboard-teacher', action: () => setIsFormateursOpen(true) }
  ]

  return (
    <div className="bg-white shadow-md sticky top-0 z-20">
      <div className="container mx-auto px-4">
        <div className="flex overflow-x-auto py-4 no-scrollbar">
          {tabs.map((tab) => (
            <button
              key={tab.id}
              onClick={() => {
                setActiveTab(tab.id)
                tab.action()
              }}
              className={`px-6 py-3 font-medium mr-4 whitespace-nowrap border-b-2 transition-colors rounded-button cursor-pointer ${
                activeTab === tab.id
                  ? 'border-blue-800 text-blue-800'
                  : 'border-transparent text-gray-600 hover:text-blue-800'
              }`}
            >
              <i className={`${tab.icon} mr-2`}></i> {tab.label}
            </button>
          ))}
        </div>
      </div>

      {/* Modales */}
      <ModaleTemoignages
        isOpen={isTemoignagesOpen}
        onClose={() => setIsTemoignagesOpen(false)}
      />

      <ModalePartenaires
        isOpen={isPartenairesOpen}
        onClose={() => setIsPartenairesOpen(false)}
      />

      <ModaleEquipes
        isOpen={isEquipesOpen}
        onClose={() => setIsEquipesOpen(false)}
      />

      <ModaleFormateurs
        isOpen={isFormateursOpen}
        onClose={() => setIsFormateursOpen(false)}
      />
    </div>
  )
}

export default ServiceTabs
