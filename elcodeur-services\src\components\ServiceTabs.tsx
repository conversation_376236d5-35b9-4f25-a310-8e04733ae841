import React, { useState } from 'react'

const ServiceTabs: React.FC = () => {
  const [activeTab, setActiveTab] = useState('dev-web')

  const tabs = [
    { id: 'formations', label: 'Formations', icon: 'fas fa-graduation-cap' },
    { id: 'design', label: 'Conceptions Graphiques', icon: 'fas fa-paint-brush' },
    { id: 'dev-web', label: 'Développement Web', icon: 'fas fa-code' }
  ]

  return (
    <div className="bg-white shadow-md sticky top-0 z-20">
      <div className="container mx-auto px-4">
        <div className="flex overflow-x-auto py-4 no-scrollbar">
          {tabs.map((tab) => (
            <button
              key={tab.id}
              onClick={() => setActiveTab(tab.id)}
              className={`px-6 py-3 font-medium mr-4 whitespace-nowrap border-b-2 transition-colors rounded-button cursor-pointer ${
                activeTab === tab.id
                  ? 'border-blue-800 text-blue-800'
                  : 'border-transparent text-gray-600 hover:text-blue-800'
              }`}
            >
              <i className={`${tab.icon} mr-2`}></i> {tab.label}
            </button>
          ))}
        </div>
      </div>
    </div>
  )
}

export default ServiceTabs
