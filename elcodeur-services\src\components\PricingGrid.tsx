import React from 'react'

interface PricingGridProps {
  onAddToPanier: (item: {
    id: string
    title: string
    price: number
    description: string
  }) => void
}

const PricingGrid: React.FC<PricingGridProps> = ({ onAddToPanier }) => {
  const pricingPlans = [
    {
      id: 'site-vitrine',
      title: 'Site Vitrine',
      description: 'Idéal pour les petites entreprises et professionnels',
      price: 'À partir de 250 000 FCFA',
      priceNumeric: 250000,
      features: [
        'Jusqu\'à 5 pages',
        'Design responsive',
        'Formulaire de contact',
        'Intégration réseaux sociaux',
        'Optimisation SEO de base',
        'Hébergement 1 an inclus'
      ],
      buttonText: 'Commander',
      buttonStyle: 'bg-gray-800 hover:bg-gray-900',
      popular: false
    },
    {
      id: 'site-ecommerce',
      title: 'Site E-commerce',
      description: 'Solution complète pour vendre en ligne',
      price: 'À partir de 450 000 FCFA',
      priceNumeric: 450000,
      features: [
        'Jusqu\'à 100 produits',
        'Panier d\'achat',
        'Système de paiement sécurisé',
        'Gestion des stocks',
        'Tableau de bord administrateur',
        'Hébergement 1 an inclus',
        'Formation à l\'utilisation'
      ],
      buttonText: 'Commander',
      buttonStyle: 'bg-blue-800 hover:bg-blue-900',
      popular: true
    },
    {
      id: 'application-web',
      title: 'Application Web',
      description: 'Développement sur mesure pour besoins spécifiques',
      price: 'À partir de 800 000 FCFA',
      priceNumeric: 800000,
      features: [
        'Analyse des besoins',
        'Conception sur mesure',
        'Développement frontend et backend',
        'Base de données sécurisée',
        'API et intégrations',
        'Tests et déploiement',
        'Formation et documentation'
      ],
      buttonText: 'Commander',
      buttonStyle: 'bg-gray-800 hover:bg-gray-900',
      popular: false
    }
  ]

  return (
    <div className="mb-16">
      <h3 className="text-2xl font-bold mb-6 text-gray-800">Grille tarifaire</h3>
      <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
        {pricingPlans.map((plan, index) => (
          <div key={index} className={`bg-white rounded-lg shadow-lg overflow-hidden h-full flex flex-col relative ${
            plan.popular ? 'border border-blue-500' : 'border border-gray-200'
          }`}>
            {plan.popular && (
              <div className="absolute top-0 right-0">
                <div className="bg-blue-500 text-white text-xs font-bold px-3 py-1 rounded-bl-lg">
                  POPULAIRE
                </div>
              </div>
            )}
            <div className={`p-6 ${plan.popular ? 'bg-blue-50' : ''}`}>
              <h4 className="text-xl font-bold mb-2">{plan.title}</h4>
              <p className="text-gray-600 text-sm mb-4">{plan.description}</p>
              <div className="mb-6">
                <span className="text-3xl font-bold text-gray-800">{plan.price}</span>
              </div>
            </div>
            <div className="p-6 flex-grow">
              <ul className="space-y-3 mb-6">
                {plan.features.map((feature, featureIndex) => (
                  <li key={featureIndex} className="flex items-start">
                    <i className="fas fa-check text-green-500 mt-1 mr-2"></i>
                    <span>{feature}</span>
                  </li>
                ))}
              </ul>
            </div>
            <div className="p-6">
              <button
                onClick={() => onAddToPanier({
                  id: plan.id,
                  title: plan.title,
                  price: plan.priceNumeric,
                  description: plan.description
                })}
                className={`w-full ${plan.buttonStyle} text-white px-4 py-3 rounded-lg font-medium transition-colors rounded-button cursor-pointer whitespace-nowrap`}
              >
                <i className="fas fa-shopping-cart mr-2"></i>
                {plan.buttonText}
              </button>
            </div>
          </div>
        ))}
      </div>
      <div className="mt-6 text-center text-gray-600">
        <p>Tous nos tarifs incluent la conception, le développement, les tests et la mise en ligne.</p>
        <p>Maintenance et support technique disponibles avec forfaits mensuels à partir de 25 000 FCFA/mois.</p>
      </div>
    </div>
  )
}

export default PricingGrid
