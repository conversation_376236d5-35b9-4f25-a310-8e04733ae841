import React from 'react'

const TechStack: React.FC = () => {
  const technologies = [
    { icon: 'fab fa-html5', name: 'HTML5 / CSS3', bgColor: 'bg-orange-100', textColor: 'text-orange-600', delay: '0s' },
    { icon: 'fab fa-js', name: 'JavaScript', bgColor: 'bg-yellow-100', textColor: 'text-yellow-600', delay: '0.2s' },
    { icon: 'fab fa-php', name: 'PHP', bgColor: 'bg-indigo-100', textColor: 'text-indigo-600', delay: '0.4s' },
    { icon: 'fab fa-wordpress', name: 'WordPress', bgColor: 'bg-blue-100', textColor: 'text-blue-600', delay: '0.6s' },
    { icon: 'fab fa-react', name: 'React', bgColor: 'bg-cyan-100', textColor: 'text-cyan-600', delay: '0.8s' },
    { icon: 'fab fa-node-js', name: 'Node.js', bgColor: 'bg-green-100', textColor: 'text-green-600', delay: '1s' },
    { icon: 'fas fa-database', name: 'MySQL', bgColor: 'bg-blue-100', textColor: 'text-blue-600', delay: '1.2s' },
    { icon: 'fab fa-bootstrap', name: 'Bootstrap', bgColor: 'bg-purple-100', textColor: 'text-purple-600', delay: '1.4s' }
  ]

  return (
    <div className="mb-16">
      <h3 className="text-2xl font-bold mb-6 text-gray-800">Technologies maîtrisées</h3>
      <div className="grid grid-cols-2 md:grid-cols-4 gap-6">
        {technologies.map((tech, index) => (
          <div key={index} className="bg-white rounded-lg shadow-sm border border-gray-100 p-6 flex items-center hover:shadow-lg transition-all duration-300">
            <div
              className={`w-12 h-12 rounded-full ${tech.bgColor} flex items-center justify-center ${tech.textColor} mr-4 tech-icon-flare`}
              style={{ animationDelay: tech.delay }}
            >
              <i className={`${tech.icon} text-2xl`}></i>
            </div>
            <span className="font-medium">{tech.name}</span>
          </div>
        ))}
      </div>
    </div>
  )
}

export default TechStack
