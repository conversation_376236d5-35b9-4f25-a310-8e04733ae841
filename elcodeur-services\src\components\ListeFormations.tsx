import React, { useState } from 'react'
import NetworkAnimation from './NetworkAnimation'
import FormulaireInscriptionFormation from './FormulaireInscriptionFormation'

interface Formation {
  id: number
  icon: string
  title: string
  description: string
  modules: string[]
  duree: string
  prixNormal: string
  prixPromo: string
  reduction: string
  niveau: string
  prerequis: string[]
  objectifs: string[]
  outils: string[]
  certification: string
  avantages: string[]
}

interface ListeFormationsProps {
  isOpen: boolean
  onClose: () => void
}

const ListeFormations: React.FC<ListeFormationsProps> = ({ isOpen, onClose }) => {
  const [selectedFormation, setSelectedFormation] = useState<Formation | null>(null)
  const [isHovering, setIsHovering] = useState(false)
  const [isInscriptionOpen, setIsInscriptionOpen] = useState(false)
  const [formationPourInscription, setFormationPourInscription] = useState<Formation | null>(null)

  const handleInscription = (formation: Formation) => {
    setFormationPourInscription(formation)
    setIsInscriptionOpen(true)
  }

  const formations: Formation[] = [
    {
      id: 1,
      icon: 'fas fa-paint-brush',
      title: 'Formation Infographie',
      description: 'Maîtrisez les outils de création graphique professionnels et développez votre créativité visuelle.',
      modules: [
        'Photoshop - Retouche et montage photo',
        'Illustrator - Création vectorielle',
        'InDesign - Mise en page professionnelle',
        'Canva - Design rapide et efficace',
        'Théorie des couleurs et composition',
        'Création de logos et identité visuelle',
        'Design web et mobile',
        'Projet final - Portfolio professionnel'
      ],
      duree: '6 semaines',
      prixNormal: '150 000 FCFA',
      prixPromo: '89 000 FCFA',
      reduction: '40%',
      niveau: 'Débutant à Intermédiaire',
      prerequis: [
        'Connaissance de base de l\'ordinateur',
        'Créativité et sens artistique',
        'Motivation pour apprendre'
      ],
      objectifs: [
        'Maîtriser les logiciels Adobe Creative Suite',
        'Créer des designs professionnels',
        'Développer son portfolio graphique',
        'Comprendre les principes du design',
        'Être autonome en création graphique'
      ],
      outils: ['Photoshop', 'Illustrator', 'InDesign', 'Canva', 'Figma'],
      certification: 'Certificat de Formation Professionnelle en Infographie',
      avantages: [
        'Formation pratique avec projets réels',
        'Suivi personnalisé par des experts',
        'Accès aux logiciels professionnels',
        'Portfolio professionnel à la fin',
        'Opportunités de stage et emploi'
      ]
    },
    {
      id: 2,
      icon: 'fas fa-cogs',
      title: 'Administration de Site Web',
      description: 'Apprenez à gérer, maintenir et optimiser des sites web avec les meilleures pratiques.',
      modules: [
        'Introduction aux CMS (WordPress, Drupal)',
        'Gestion de contenu et médias',
        'Sécurité web et sauvegardes',
        'Optimisation SEO et performance',
        'Analytics et suivi de trafic',
        'Maintenance préventive',
        'Gestion des utilisateurs et permissions',
        'Projet pratique - Site complet'
      ],
      duree: '4 semaines',
      prixNormal: '120 000 FCFA',
      prixPromo: '75 000 FCFA',
      reduction: '37%',
      niveau: 'Débutant',
      prerequis: [
        'Utilisation courante d\'internet',
        'Notions de base en informatique',
        'Aucune expérience technique requise'
      ],
      objectifs: [
        'Administrer un site web en autonomie',
        'Assurer la sécurité et maintenance',
        'Optimiser le référencement naturel',
        'Analyser les performances du site',
        'Gérer le contenu efficacement'
      ],
      outils: ['WordPress', 'Google Analytics', 'SEO Tools', 'cPanel', 'FTP'],
      certification: 'Certificat d\'Administration Web Professionnelle',
      avantages: [
        'Formation 100% pratique',
        'Site web personnel inclus',
        'Support technique continu',
        'Mise à jour des compétences',
        'Réseau professionnel'
      ]
    },
    {
      id: 3,
      icon: 'fas fa-file-alt',
      title: 'CV Pro',
      description: 'Créez un CV professionnel percutant et optimisez votre présence en ligne pour décrocher l\'emploi de vos rêves.',
      modules: [
        'Analyse de profil professionnel',
        'Rédaction CV moderne et ATS-friendly',
        'Lettre de motivation personnalisée',
        'Optimisation profil LinkedIn',
        'Portfolio en ligne professionnel',
        'Techniques de recherche d\'emploi',
        'Préparation aux entretiens',
        'Personal branding digital'
      ],
      duree: '2 semaines',
      prixNormal: '80 000 FCFA',
      prixPromo: '45 000 FCFA',
      reduction: '43%',
      niveau: 'Tous niveaux',
      prerequis: [
        'Expérience professionnelle (stage inclus)',
        'Motivation pour recherche d\'emploi',
        'Accès à internet'
      ],
      objectifs: [
        'CV professionnel et moderne',
        'Profil LinkedIn optimisé',
        'Portfolio en ligne attractif',
        'Stratégie de recherche d\'emploi',
        'Confiance en entretien'
      ],
      outils: ['Canva', 'LinkedIn', 'Google Sites', 'Word/PDF', 'Templates Pro'],
      certification: 'Certificat de Personal Branding Professionnel',
      avantages: [
        'CV personnalisé selon votre domaine',
        'Révision illimitée pendant 3 mois',
        'Coaching personnalisé',
        'Templates exclusifs',
        'Garantie satisfaction'
      ]
    }
  ]

  if (!isOpen) return null

  return (
    <div className="fixed inset-0 bg-white flex items-center justify-center z-50 p-4">
      {/* Animation de réseau en arrière-plan */}
      <NetworkAnimation isExploding={isHovering} />
      
      <div 
        className="bg-white bg-opacity-95 backdrop-blur-sm rounded-2xl max-w-7xl w-full max-h-[90vh] overflow-hidden shadow-2xl border border-gray-200 relative z-10"
        onMouseEnter={() => setIsHovering(true)}
        onMouseLeave={() => setIsHovering(false)}
      >
        {/* En-tête fixe */}
        <div className="bg-gradient-to-r from-green-600 to-blue-600 text-white p-6 flex justify-between items-center">
          <div>
            <h2 className="text-3xl font-bold mb-2 flex items-center">
              Formations
              <span className="ml-3 bg-red-500 text-white px-3 py-1 rounded-full text-sm font-bold animate-pulse">
                PROMO !
              </span>
            </h2>
            <p className="text-green-100">Développez vos compétences avec nos formations professionnelles à prix réduit</p>
          </div>
          <button
            onClick={onClose}
            className="text-white hover:text-gray-200 text-2xl font-bold w-10 h-10 flex items-center justify-center rounded-full hover:bg-white hover:bg-opacity-20 transition-all cursor-pointer"
          >
            ×
          </button>
        </div>

        {/* Contenu scrollable */}
        <div className="overflow-y-auto max-h-[calc(90vh-120px)] p-6">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {formations.map((formation) => (
              <div key={formation.id} className="bg-white border border-gray-200 rounded-xl overflow-hidden hover:shadow-lg transition-all duration-300 relative">
                {/* Badge de réduction */}
                <div className="absolute top-4 right-4 bg-red-500 text-white px-2 py-1 rounded-full text-xs font-bold z-10">
                  -{formation.reduction}
                </div>

                <div className="bg-gradient-to-r from-green-600 to-blue-600 text-white p-4">
                  <div className="flex items-center justify-between mb-3">
                    <div className="w-12 h-12 bg-white bg-opacity-20 rounded-full flex items-center justify-center">
                      <i className={`${formation.icon} text-xl`}></i>
                    </div>
                    <span className="text-sm bg-white bg-opacity-20 px-2 py-1 rounded-full">
                      {formation.niveau}
                    </span>
                  </div>
                  <h3 className="text-xl font-bold mb-2">{formation.title}</h3>
                  <p className="text-green-100 text-sm">{formation.description}</p>
                </div>

                <div className="p-4">
                  <div className="mb-4">
                    <div className="flex justify-between items-center mb-2">
                      <span className="text-sm font-medium text-gray-600">Prix normal</span>
                      <span className="text-sm text-gray-500 line-through">{formation.prixNormal}</span>
                    </div>
                    <div className="flex justify-between items-center mb-2">
                      <span className="text-sm font-medium text-gray-600">Prix promo</span>
                      <span className="text-lg font-bold text-green-600">{formation.prixPromo}</span>
                    </div>
                    <div className="flex justify-between items-center">
                      <span className="text-sm font-medium text-gray-600">Durée</span>
                      <span className="text-sm text-gray-800">{formation.duree}</span>
                    </div>
                  </div>

                  <div className="mb-4">
                    <h4 className="font-semibold text-gray-800 mb-2">Modules principaux :</h4>
                    <ul className="space-y-1">
                      {formation.modules.slice(0, 3).map((module, index) => (
                        <li key={index} className="flex items-start text-sm">
                          <i className="fas fa-check text-green-500 mt-1 mr-2 text-xs"></i>
                          <span className="text-gray-600">{module}</span>
                        </li>
                      ))}
                      {formation.modules.length > 3 && (
                        <li className="text-sm text-blue-600">
                          +{formation.modules.length - 3} autres modules
                        </li>
                      )}
                    </ul>
                  </div>

                  <div className="flex gap-2">
                    <button
                      onClick={() => handleInscription(formation)}
                      className="flex-1 bg-green-600 hover:bg-green-700 text-white px-3 py-2 rounded-lg text-sm font-medium transition-colors cursor-pointer"
                    >
                      S'inscrire
                    </button>
                    <button
                      onClick={() => setSelectedFormation(formation)}
                      className="px-3 py-2 border border-gray-300 rounded-lg text-sm font-medium text-gray-700 hover:bg-gray-50 transition-colors cursor-pointer"
                    >
                      Détails
                    </button>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* Modale de détails de la formation */}
      {selectedFormation && (
        <div className="fixed inset-0 bg-white flex items-center justify-center z-60 p-4">
          {/* Animation de réseau pour la modale de détails */}
          <NetworkAnimation isExploding={true} />
          
          <div className="bg-white bg-opacity-95 backdrop-blur-sm rounded-2xl max-w-5xl w-full max-h-[90vh] overflow-hidden shadow-2xl border border-gray-200 relative z-10">
            <div className="bg-gradient-to-r from-green-600 to-blue-600 text-white p-6 flex justify-between items-center">
              <div>
                <h3 className="text-2xl font-bold mb-2 flex items-center">
                  {selectedFormation.title}
                  <span className="ml-3 bg-red-500 text-white px-2 py-1 rounded-full text-xs font-bold">
                    -{selectedFormation.reduction}
                  </span>
                </h3>
                <p className="text-green-100">{selectedFormation.description}</p>
              </div>
              <button
                onClick={() => setSelectedFormation(null)}
                className="text-white hover:text-gray-200 text-2xl font-bold w-10 h-10 flex items-center justify-center rounded-full hover:bg-white hover:bg-opacity-20 transition-all cursor-pointer"
              >
                ×
              </button>
            </div>

            <div className="overflow-y-auto max-h-[calc(90vh-120px)] p-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <div className="mb-6">
                    <h4 className="text-lg font-bold text-gray-800 mb-3">Informations générales</h4>
                    <div className="space-y-2">
                      <div className="flex justify-between">
                        <span className="font-medium text-gray-600">Prix normal :</span>
                        <span className="text-gray-500 line-through">{selectedFormation.prixNormal}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="font-medium text-gray-600">Prix promo :</span>
                        <span className="text-green-600 font-bold">{selectedFormation.prixPromo}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="font-medium text-gray-600">Durée :</span>
                        <span className="text-gray-800">{selectedFormation.duree}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="font-medium text-gray-600">Niveau :</span>
                        <span className="text-gray-800">{selectedFormation.niveau}</span>
                      </div>
                    </div>
                  </div>

                  <div className="mb-6">
                    <h4 className="text-lg font-bold text-gray-800 mb-3">Outils utilisés</h4>
                    <div className="flex flex-wrap gap-2">
                      {selectedFormation.outils.map((outil, index) => (
                        <span key={index} className="bg-green-100 text-green-800 px-3 py-1 rounded-full text-sm font-medium">
                          {outil}
                        </span>
                      ))}
                    </div>
                  </div>

                  <div>
                    <h4 className="text-lg font-bold text-gray-800 mb-3">Prérequis</h4>
                    <ul className="space-y-2">
                      {selectedFormation.prerequis.map((prerequis, index) => (
                        <li key={index} className="flex items-start">
                          <i className="fas fa-info-circle text-blue-500 mt-1 mr-2"></i>
                          <span className="text-gray-700">{prerequis}</span>
                        </li>
                      ))}
                    </ul>
                  </div>
                </div>

                <div>
                  <div className="mb-6">
                    <h4 className="text-lg font-bold text-gray-800 mb-3">Modules complets</h4>
                    <ul className="space-y-2">
                      {selectedFormation.modules.map((module, index) => (
                        <li key={index} className="flex items-start">
                          <span className="bg-green-600 text-white rounded-full w-6 h-6 flex items-center justify-center text-sm font-bold mr-3 mt-0.5">
                            {index + 1}
                          </span>
                          <span className="text-gray-700">{module}</span>
                        </li>
                      ))}
                    </ul>
                  </div>

                  <div className="mb-6">
                    <h4 className="text-lg font-bold text-gray-800 mb-3">Objectifs de formation</h4>
                    <ul className="space-y-2">
                      {selectedFormation.objectifs.map((objectif, index) => (
                        <li key={index} className="flex items-start">
                          <i className="fas fa-target text-green-500 mt-1 mr-2"></i>
                          <span className="text-gray-700">{objectif}</span>
                        </li>
                      ))}
                    </ul>
                  </div>

                  <div>
                    <h4 className="text-lg font-bold text-gray-800 mb-3">Avantages inclus</h4>
                    <ul className="space-y-2">
                      {selectedFormation.avantages.map((avantage, index) => (
                        <li key={index} className="flex items-start">
                          <i className="fas fa-star text-yellow-500 mt-1 mr-2"></i>
                          <span className="text-gray-700">{avantage}</span>
                        </li>
                      ))}
                    </ul>
                  </div>
                </div>
              </div>

              <div className="mt-8 bg-gray-50 p-4 rounded-lg">
                <h4 className="text-lg font-bold text-gray-800 mb-2">Certification</h4>
                <p className="text-gray-700 flex items-center">
                  <i className="fas fa-certificate text-yellow-500 mr-2"></i>
                  {selectedFormation.certification}
                </p>
              </div>

              <div className="mt-8 flex gap-4 justify-center">
                <button
                  onClick={() => {
                    handleInscription(selectedFormation);
                    setSelectedFormation(null);
                  }}
                  className="bg-green-600 hover:bg-green-700 text-white px-6 py-3 rounded-lg font-medium transition-colors cursor-pointer"
                >
                  <i className="fas fa-graduation-cap mr-2"></i>
                  S'inscrire maintenant
                </button>
                <button
                  onClick={() => setSelectedFormation(null)}
                  className="bg-gray-500 hover:bg-gray-600 text-white px-6 py-3 rounded-lg font-medium transition-colors cursor-pointer"
                >
                  Fermer
                </button>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Formulaire d'inscription */}
      <FormulaireInscriptionFormation
        isOpen={isInscriptionOpen}
        onClose={() => {
          setIsInscriptionOpen(false)
          setFormationPourInscription(null)
        }}
        formation={formationPourInscription}
      />
    </div>
  )
}

export default ListeFormations
