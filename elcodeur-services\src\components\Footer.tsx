import React from 'react'

const Footer: React.FC = () => {
  return (
    <footer className="bg-gray-900 text-white py-12">
      <div className="container mx-auto px-4">
        <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
          <div>
            <div className="flex items-center mb-4">
              <span className="text-3xl font-bold">
                <span className="text-white">e</span>
                <span className="text-red-500">L</span>
                <span className="text-white">S</span>
              </span>
              <span className="ml-2 text-gray-300 font-serif italic">elCodeur Services.</span>
            </div>
            <p className="text-gray-400 mb-4">
              Solutions numériques professionnelles pour tous vos besoins informatiques.
            </p>
            <div className="flex space-x-4">
              <a href="#" className="text-gray-400 hover:text-white transition-colors cursor-pointer">
                <i className="fab fa-facebook-f"></i>
              </a>
              <a href="#" className="text-gray-400 hover:text-white transition-colors cursor-pointer">
                <i className="fab fa-twitter"></i>
              </a>
              <a href="#" className="text-gray-400 hover:text-white transition-colors cursor-pointer">
                <i className="fab fa-instagram"></i>
              </a>
              <a href="#" className="text-gray-400 hover:text-white transition-colors cursor-pointer">
                <i className="fab fa-linkedin-in"></i>
              </a>
            </div>
          </div>
          
          <div>
            <h3 className="text-xl font-bold mb-4">Nos Services</h3>
            <ul className="space-y-2">
              <li>
                <a href="#" className="text-gray-400 hover:text-white transition-colors cursor-pointer">
                  Formations Informatiques
                </a>
              </li>
              <li>
                <a href="#" className="text-gray-400 hover:text-white transition-colors cursor-pointer">
                  Conceptions Graphiques
                </a>
              </li>
              <li>
                <a href="#" className="text-gray-400 hover:text-white transition-colors cursor-pointer">
                  Développement Web
                </a>
              </li>
               <li>
                <a href="#" className="text-gray-400 hover:text-white transition-colors cursor-pointer">
                  Développement d'Applications Mobiles
                </a>
              </li>
              <li>
                <a href="#" className="text-gray-400 hover:text-white transition-colors cursor-pointer">
                  Montage Vidéo
                </a>
              </li>
              <li>
                <a href="#" className="text-gray-400 hover:text-white transition-colors cursor-pointer">
                  Infographie
                </a>
              </li>
            </ul>
          </div>
          
          <div>
            <h3 className="text-xl font-bold mb-4">Contact</h3>
            <ul className="space-y-2">
              <li className="flex items-center">
                <i className="fas fa-phone-alt mr-2 text-gray-400"></i>
                <span>+225 05 56 75 04 67 | 07 78 63 92 48</span>
              </li>
              <li className="flex items-center">
                <i className="fas fa-envelope mr-2 text-gray-400"></i>
                <span><EMAIL></span>
              </li>
               <li className="flex items-center">
                <i className="fas fa-envelope mr-2 text-gray-400"></i>
                <span><EMAIL></span>
              </li>
              <li className="flex items-center">
                <i className="fas fa-map-marker-alt mr-2 text-gray-400"></i>
                <span>Abidjan, Côte d'Ivoire</span>
              </li>
            </ul>
            <div className="mt-4">
              <p className="text-sm text-gray-500">© 2025 elCodeur Services. Tous droits réservés.</p>
              <p className="text-sm text-gray-500">Design par ELIE MAHOUAN DESIGN</p>
            </div>
          </div>
        </div>
      </div>
    </footer>
  )
}

export default Footer
