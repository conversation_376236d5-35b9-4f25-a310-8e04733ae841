import React from 'react'

interface Projet {
  id: number
  image: string
  alt: string
  category: string
  tech: string
  description: string
  dateRealisation: string
  client?: string
  statut: 'Terminé' | 'En cours' | 'En maintenance'
  descriptionComplete: string
  fonctionnalites: string[]
  technologies: string[]
  dureeProjet: string
  equipe: string[]
  defis: string[]
  resultats: string[]
  lienDemo?: string
  lienGithub?: string
}

interface DetailRealisationProps {
  isOpen: boolean
  onClose: () => void
  projet: Projet | null
}

const DetailRealisation: React.FC<DetailRealisationProps> = ({ isOpen, onClose, projet }) => {
  if (!isOpen || !projet) return null

  const getStatutColor = (statut: string) => {
    switch (statut) {
      case 'Terminé':
        return 'bg-green-100 text-green-800'
      case 'En cours':
        return 'bg-blue-100 text-blue-800'
      case 'En maintenance':
        return 'bg-orange-100 text-orange-800'
      default:
        return 'bg-gray-100 text-gray-800'
    }
  }

  return (
    <div 
      className="fixed inset-0 bg-white bg-opacity-95 z-50 flex items-center justify-center p-4 cursor-pointer"
      onClick={onClose}
    >
      <div 
        className="bg-white rounded-2xl shadow-2xl w-full max-w-6xl max-h-[90vh] overflow-hidden cursor-default"
        onClick={(e) => e.stopPropagation()}
      >
        {/* En-tête */}
        <div className="bg-gradient-to-r from-red-800 to-blue-800 p-6 text-white">
          <div className="flex items-center justify-between">
            <div className="flex-1">
              <div className="flex items-center gap-4 mb-2">
                <h2 className="text-2xl font-bold">{projet.alt}</h2>
                <span className={`px-3 py-1 rounded-full text-xs font-medium ${getStatutColor(projet.statut)}`}>
                  {projet.statut}
                </span>
              </div>
              <p className="text-blue-100 mb-2">{projet.category}</p>
              <div className="flex items-center gap-4 text-sm">
                <span className="flex items-center">
                  <i className="fas fa-calendar mr-2"></i>
                  {projet.dateRealisation}
                </span>
                {projet.client && (
                  <span className="flex items-center">
                    <i className="fas fa-building mr-2"></i>
                    {projet.client}
                  </span>
                )}
                <span className="flex items-center">
                  <i className="fas fa-clock mr-2"></i>
                  {projet.dureeProjet}
                </span>
              </div>
            </div>
            <button
              onClick={onClose}
              className="p-2 hover:bg-white hover:bg-opacity-20 rounded-full transition-all duration-300 cursor-pointer"
            >
              <i className="fas fa-times text-2xl"></i>
            </button>
          </div>
        </div>

        {/* Contenu scrollable */}
        <div className="p-6 overflow-y-auto max-h-[calc(90vh-140px)]">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
            
            {/* Colonne gauche - Image et informations principales */}
            <div>
              {/* Image principale */}
              <div className="mb-6">
                <img
                  src={projet.image}
                  alt={projet.alt}
                  className="w-full h-64 object-cover rounded-xl shadow-lg"
                />
              </div>

              {/* Description complète */}
              <div className="bg-gradient-to-br from-gray-50 to-blue-50 p-6 rounded-xl mb-6">
                <h3 className="text-xl font-bold text-gray-800 mb-4 flex items-center">
                  <i className="fas fa-info-circle mr-3 text-blue-600"></i>
                  Description du projet
                </h3>
                <p className="text-gray-700 leading-relaxed">
                  {projet.descriptionComplete}
                </p>
              </div>

              {/* Technologies utilisées */}
              <div className="bg-gradient-to-br from-red-50 to-orange-50 p-6 rounded-xl">
                <h3 className="text-xl font-bold text-gray-800 mb-4 flex items-center">
                  <i className="fas fa-code mr-3 text-red-600"></i>
                  Technologies utilisées
                </h3>
                <div className="flex flex-wrap gap-2">
                  {projet.technologies.map((tech, index) => (
                    <span 
                      key={index}
                      className="bg-red-100 text-red-800 px-3 py-1 rounded-full text-sm font-medium"
                    >
                      {tech}
                    </span>
                  ))}
                </div>
              </div>
            </div>

            {/* Colonne droite - Détails techniques */}
            <div className="space-y-6">
              
              {/* Fonctionnalités principales */}
              <div className="bg-gradient-to-br from-blue-50 to-cyan-50 p-6 rounded-xl">
                <h3 className="text-xl font-bold text-gray-800 mb-4 flex items-center">
                  <i className="fas fa-list-check mr-3 text-blue-600"></i>
                  Fonctionnalités principales
                </h3>
                <ul className="space-y-2">
                  {projet.fonctionnalites.map((fonctionnalite, index) => (
                    <li key={index} className="flex items-start">
                      <i className="fas fa-check-circle text-green-500 mr-3 mt-1"></i>
                      <span className="text-gray-700">{fonctionnalite}</span>
                    </li>
                  ))}
                </ul>
              </div>

              {/* Équipe projet */}
              <div className="bg-gradient-to-br from-purple-50 to-pink-50 p-6 rounded-xl">
                <h3 className="text-xl font-bold text-gray-800 mb-4 flex items-center">
                  <i className="fas fa-users mr-3 text-purple-600"></i>
                  Équipe projet
                </h3>
                <div className="flex flex-wrap gap-2">
                  {projet.equipe.map((membre, index) => (
                    <span 
                      key={index}
                      className="bg-purple-100 text-purple-800 px-3 py-1 rounded-full text-sm font-medium"
                    >
                      {membre}
                    </span>
                  ))}
                </div>
              </div>

              {/* Défis techniques */}
              <div className="bg-gradient-to-br from-orange-50 to-yellow-50 p-6 rounded-xl">
                <h3 className="text-xl font-bold text-gray-800 mb-4 flex items-center">
                  <i className="fas fa-exclamation-triangle mr-3 text-orange-600"></i>
                  Défis techniques
                </h3>
                <ul className="space-y-2">
                  {projet.defis.map((defi, index) => (
                    <li key={index} className="flex items-start">
                      <i className="fas fa-cog text-orange-500 mr-3 mt-1"></i>
                      <span className="text-gray-700">{defi}</span>
                    </li>
                  ))}
                </ul>
              </div>

              {/* Résultats obtenus */}
              <div className="bg-gradient-to-br from-green-50 to-emerald-50 p-6 rounded-xl">
                <h3 className="text-xl font-bold text-gray-800 mb-4 flex items-center">
                  <i className="fas fa-trophy mr-3 text-green-600"></i>
                  Résultats obtenus
                </h3>
                <ul className="space-y-2">
                  {projet.resultats.map((resultat, index) => (
                    <li key={index} className="flex items-start">
                      <i className="fas fa-star text-yellow-500 mr-3 mt-1"></i>
                      <span className="text-gray-700">{resultat}</span>
                    </li>
                  ))}
                </ul>
              </div>
            </div>
          </div>

          {/* Liens d'action */}
          <div className="mt-8 pt-6 border-t border-gray-200">
            <div className="flex flex-wrap gap-4 justify-center">
              {projet.lienDemo && (
                <a
                  href={projet.lienDemo}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="bg-gradient-to-r from-blue-600 to-cyan-600 text-white px-6 py-3 rounded-lg font-medium hover:from-blue-700 hover:to-cyan-700 transition-all duration-300 transform hover:scale-105 cursor-pointer"
                >
                  <i className="fas fa-external-link-alt mr-2"></i>
                  Voir la démo
                </a>
              )}
              {projet.lienGithub && (
                <a
                  href={projet.lienGithub}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="bg-gradient-to-r from-gray-700 to-gray-900 text-white px-6 py-3 rounded-lg font-medium hover:from-gray-800 hover:to-black transition-all duration-300 transform hover:scale-105 cursor-pointer"
                >
                  <i className="fab fa-github mr-2"></i>
                  Voir le code
                </a>
              )}
              <button
                onClick={onClose}
                className="bg-gradient-to-r from-red-600 to-pink-600 text-white px-6 py-3 rounded-lg font-medium hover:from-red-700 hover:to-pink-700 transition-all duration-300 transform hover:scale-105 cursor-pointer"
              >
                <i className="fas fa-arrow-left mr-2"></i>
                Retour à la liste
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}

export default DetailRealisation
