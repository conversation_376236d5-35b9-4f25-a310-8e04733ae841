import React, { useState } from 'react'
import NetworkAnimation from './NetworkAnimation'

interface Membre {
  id: number
  nom: string
  poste: string
  specialites: string[]
  experience: string
  photo: string
  description: string
  competences: string[]
  projetsRealises: number
  linkedin: string
  email: string
  langues: string[]
}

interface ModaleEquipesProps {
  isOpen: boolean
  onClose: () => void
}

const ModaleEquipes: React.FC<ModaleEquipesProps> = ({ isOpen, onClose }) => {
  const [isHovering, setIsHovering] = useState(false)
  const [selectedMembre, setSelectedMembre] = useState<Membre | null>(null)

  const equipe: Membre[] = [
    {
      id: 1,
      nom: "TOMEKPA ELIE MAHOUAN",
      poste: "Fondateur & Développeur Principal",
      specialites: ["Développement Full-Stack", "Architecture Logicielle", "Gestion de Projet"],
      experience: "8+ ans",
      photo: "/team-elie.jpg",
      description: "Passionné par l'innovation technologique, Elie dirige l'équipe avec une vision claire : transformer les idées en solutions digitales performantes. Expert en développement web et mobile, il supervise tous les projets stratégiques.",
      competences: ["React", "Node.js", "Python", "AWS", "Docker", "MongoDB", "PostgreSQL", "React Native"],
      projetsRealises: 150,
      linkedin: "linkedin.com/in/elie-mahouan",
      email: "<EMAIL>",
      langues: ["Français", "Anglais", "Espagnol"]
    },
    {
      id: 2,
      nom: "Sarah KOUAME",
      poste: "Lead Designer UI/UX",
      specialites: ["Design d'Interface", "Expérience Utilisateur", "Design Thinking"],
      experience: "6+ ans",
      photo: "/team-sarah.jpg",
      description: "Sarah transforme les concepts en expériences visuelles exceptionnelles. Spécialiste en design centré utilisateur, elle veille à ce que chaque interface soit intuitive et esthétiquement parfaite.",
      competences: ["Figma", "Adobe Creative Suite", "Sketch", "Prototyping", "User Research", "Wireframing"],
      projetsRealises: 120,
      linkedin: "linkedin.com/in/sarah-kouame",
      email: "<EMAIL>",
      langues: ["Français", "Anglais"]
    },
    {
      id: 3,
      nom: "Kofi ASANTE",
      poste: "Développeur Backend Senior",
      specialites: ["API Development", "Base de Données", "Architecture Cloud"],
      experience: "7+ ans",
      photo: "/team-kofi.jpg",
      description: "Expert en développement backend, Kofi conçoit des architectures robustes et scalables. Il est responsable de la performance et de la sécurité de nos applications.",
      competences: ["Node.js", "Python", "Java", "PostgreSQL", "Redis", "AWS", "Docker", "Kubernetes"],
      projetsRealises: 95,
      linkedin: "linkedin.com/in/kofi-asante",
      email: "<EMAIL>",
      langues: ["Français", "Anglais", "Twi"]
    },
    {
      id: 4,
      nom: "Aminata DIALLO",
      poste: "Développeuse Frontend",
      specialites: ["React", "Vue.js", "Animation Web", "Performance"],
      experience: "5+ ans",
      photo: "/team-aminata.jpg",
      description: "Aminata donne vie aux designs avec des interfaces interactives et performantes. Spécialiste des technologies frontend modernes, elle assure une expérience utilisateur fluide.",
      competences: ["React", "Vue.js", "TypeScript", "SASS", "Webpack", "Jest", "Cypress"],
      projetsRealises: 80,
      linkedin: "linkedin.com/in/aminata-diallo",
      email: "<EMAIL>",
      langues: ["Français", "Anglais", "Wolof"]
    },
    {
      id: 5,
      nom: "Jean-Baptiste KONE",
      poste: "Développeur Mobile",
      specialites: ["React Native", "Flutter", "iOS", "Android"],
      experience: "4+ ans",
      photo: "/team-jean.jpg",
      description: "Jean-Baptiste développe des applications mobiles natives et cross-platform. Il maîtrise les spécificités de chaque plateforme pour offrir des performances optimales.",
      competences: ["React Native", "Flutter", "Swift", "Kotlin", "Firebase", "App Store", "Google Play"],
      projetsRealises: 65,
      linkedin: "linkedin.com/in/jean-kone",
      email: "<EMAIL>",
      langues: ["Français", "Anglais"]
    },
    {
      id: 6,
      nom: "Fatou TRAORE",
      poste: "Formatrice & Consultante",
      specialites: ["Formation Technique", "Infographie", "Pédagogie"],
      experience: "6+ ans",
      photo: "/team-fatou.jpg",
      description: "Fatou partage son expertise à travers nos formations. Pédagogue expérimentée, elle accompagne nos apprenants vers l'excellence technique et professionnelle.",
      competences: ["Adobe Creative Suite", "Pédagogie", "Canva", "Formation", "Mentoring", "Curriculum Design"],
      projetsRealises: 200,
      linkedin: "linkedin.com/in/fatou-traore",
      email: "<EMAIL>",
      langues: ["Français", "Anglais", "Bambara"]
    }
  ]

  if (!isOpen) return null

  return (
    <div className="fixed inset-0 bg-gradient-to-br from-teal-50 to-cyan-50 flex items-center justify-center z-50 p-4">
      {/* Animation de réseau en arrière-plan */}
      <NetworkAnimation isExploding={isHovering} />
      
      <div 
        className="bg-white bg-opacity-95 backdrop-blur-sm rounded-2xl max-w-7xl w-full max-h-[90vh] overflow-hidden shadow-2xl border border-teal-200 relative z-10"
        onMouseEnter={() => setIsHovering(true)}
        onMouseLeave={() => setIsHovering(false)}
      >
        {/* En-tête fixe */}
        <div className="bg-gradient-to-r from-teal-600 to-cyan-600 text-white p-6 flex justify-between items-center">
          <div>
            <h2 className="text-3xl font-bold mb-2 flex items-center">
              <i className="fas fa-users mr-3"></i>
              Notre Équipe
            </h2>
            <p className="text-teal-100">Des experts passionnés au service de vos projets</p>
          </div>
          <button
            onClick={onClose}
            className="text-white hover:text-gray-200 text-2xl font-bold w-10 h-10 flex items-center justify-center rounded-full hover:bg-white hover:bg-opacity-20 transition-all cursor-pointer"
          >
            ×
          </button>
        </div>

        {/* Contenu scrollable */}
        <div className="overflow-y-auto max-h-[calc(90vh-120px)] p-6">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {equipe.map((membre) => (
              <div key={membre.id} className="bg-white border border-teal-200 rounded-xl overflow-hidden hover:shadow-lg transition-all duration-300">
                <div className="bg-gradient-to-r from-teal-500 to-cyan-500 text-white p-4">
                  <div className="flex items-center mb-4">
                    <div className="w-16 h-16 bg-white bg-opacity-20 rounded-full flex items-center justify-center text-white font-bold text-xl mr-4">
                      {membre.nom.split(' ').map(n => n[0]).join('').slice(0, 2)}
                    </div>
                    <div>
                      <h3 className="text-lg font-bold">{membre.nom}</h3>
                      <p className="text-teal-100 text-sm">{membre.poste}</p>
                    </div>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-sm bg-white bg-opacity-20 px-2 py-1 rounded-full">
                      {membre.experience}
                    </span>
                    <span className="text-sm font-medium">
                      {membre.projetsRealises} projets
                    </span>
                  </div>
                </div>

                <div className="p-4">
                  <div className="mb-4">
                    <h4 className="font-semibold text-gray-800 mb-2">Spécialités :</h4>
                    <div className="flex flex-wrap gap-1">
                      {membre.specialites.map((specialite, index) => (
                        <span key={index} className="bg-teal-100 text-teal-800 px-2 py-1 rounded-full text-xs font-medium">
                          {specialite}
                        </span>
                      ))}
                    </div>
                  </div>

                  <div className="mb-4">
                    <h4 className="font-semibold text-gray-800 mb-2">Compétences clés :</h4>
                    <div className="flex flex-wrap gap-1">
                      {membre.competences.slice(0, 4).map((competence, index) => (
                        <span key={index} className="bg-gray-100 text-gray-700 px-2 py-1 rounded text-xs">
                          {competence}
                        </span>
                      ))}
                      {membre.competences.length > 4 && (
                        <span className="text-xs text-teal-600">
                          +{membre.competences.length - 4}
                        </span>
                      )}
                    </div>
                  </div>

                  <p className="text-gray-600 text-sm mb-4 line-clamp-3">
                    {membre.description}
                  </p>

                  <div className="flex gap-2">
                    <button
                      onClick={() => setSelectedMembre(membre)}
                      className="flex-1 bg-teal-600 hover:bg-teal-700 text-white px-3 py-2 rounded-lg text-sm font-medium transition-colors cursor-pointer"
                    >
                      Voir profil
                    </button>
                    <button className="px-3 py-2 border border-gray-300 rounded-lg text-sm font-medium text-gray-700 hover:bg-gray-50 transition-colors cursor-pointer">
                      <i className="fab fa-linkedin"></i>
                    </button>
                  </div>
                </div>
              </div>
            ))}
          </div>

          {/* Statistiques équipe */}
          <div className="mt-8 bg-gradient-to-r from-teal-50 to-cyan-50 p-6 rounded-xl border border-teal-200">
            <h3 className="text-xl font-bold text-gray-800 mb-4 text-center">Notre force collective</h3>
            <div className="grid grid-cols-1 md:grid-cols-4 gap-4 text-center">
              <div className="bg-white p-4 rounded-lg shadow">
                <div className="text-2xl font-bold text-teal-600">6</div>
                <div className="text-sm text-gray-600">Experts dédiés</div>
              </div>
              <div className="bg-white p-4 rounded-lg shadow">
                <div className="text-2xl font-bold text-cyan-600">710+</div>
                <div className="text-sm text-gray-600">Projets réalisés</div>
              </div>
              <div className="bg-white p-4 rounded-lg shadow">
                <div className="text-2xl font-bold text-teal-600">35+</div>
                <div className="text-sm text-gray-600">Années d'expérience</div>
              </div>
              <div className="bg-white p-4 rounded-lg shadow">
                <div className="text-2xl font-bold text-cyan-600">15+</div>
                <div className="text-sm text-gray-600">Technologies maîtrisées</div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Modale de détail membre */}
      {selectedMembre && (
        <div className="fixed inset-0 bg-gradient-to-br from-teal-100 to-cyan-100 flex items-center justify-center z-60 p-4">
          {/* Animation de réseau pour la modale de détails */}
          <NetworkAnimation isExploding={true} />

          <div className="bg-white bg-opacity-95 backdrop-blur-sm rounded-2xl max-w-2xl w-full max-h-[90vh] overflow-hidden shadow-2xl border border-teal-200 relative z-10">
            <div className="bg-gradient-to-r from-teal-600 to-cyan-600 text-white p-6 flex justify-between items-center">
              <div>
                <h3 className="text-2xl font-bold">{selectedMembre.nom}</h3>
                <p className="text-teal-100">{selectedMembre.poste}</p>
              </div>
              <button
                onClick={() => setSelectedMembre(null)}
                className="text-white hover:text-gray-200 text-2xl font-bold w-10 h-10 flex items-center justify-center rounded-full hover:bg-white hover:bg-opacity-20 transition-all cursor-pointer"
              >
                ×
              </button>
            </div>

            <div className="overflow-y-auto max-h-[calc(90vh-120px)] p-6">
              <div className="space-y-6">
                <div>
                  <h4 className="text-lg font-bold text-gray-800 mb-3">À propos</h4>
                  <p className="text-gray-700">{selectedMembre.description}</p>
                </div>

                <div>
                  <h4 className="text-lg font-bold text-gray-800 mb-3">Compétences techniques</h4>
                  <div className="flex flex-wrap gap-2">
                    {selectedMembre.competences.map((competence, index) => (
                      <span key={index} className="bg-teal-100 text-teal-800 px-3 py-1 rounded-full text-sm font-medium">
                        {competence}
                      </span>
                    ))}
                  </div>
                </div>

                <div>
                  <h4 className="text-lg font-bold text-gray-800 mb-3">Langues parlées</h4>
                  <div className="flex flex-wrap gap-2">
                    {selectedMembre.langues.map((langue, index) => (
                      <span key={index} className="bg-gray-100 text-gray-700 px-3 py-1 rounded-full text-sm">
                        {langue}
                      </span>
                    ))}
                  </div>
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <div className="bg-teal-50 p-4 rounded-lg">
                    <div className="text-2xl font-bold text-teal-600">{selectedMembre.projetsRealises}</div>
                    <div className="text-sm text-gray-600">Projets réalisés</div>
                  </div>
                  <div className="bg-cyan-50 p-4 rounded-lg">
                    <div className="text-2xl font-bold text-cyan-600">{selectedMembre.experience}</div>
                    <div className="text-sm text-gray-600">Expérience</div>
                  </div>
                </div>

                <div className="flex gap-4 justify-center">
                  <button className="bg-teal-600 hover:bg-teal-700 text-white px-6 py-3 rounded-lg font-medium transition-colors cursor-pointer">
                    <i className="fab fa-linkedin mr-2"></i>
                    LinkedIn
                  </button>
                  <button className="bg-gray-500 hover:bg-gray-600 text-white px-6 py-3 rounded-lg font-medium transition-colors cursor-pointer">
                    <i className="fas fa-envelope mr-2"></i>
                    Contact
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}

export default ModaleEquipes
