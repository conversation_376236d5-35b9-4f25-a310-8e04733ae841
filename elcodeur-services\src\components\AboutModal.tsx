import React from 'react'

interface AboutModalProps {
  isOpen: boolean
  onClose: () => void
}

const AboutModal: React.FC<AboutModalProps> = ({ isOpen, onClose }) => {
  if (!isOpen) return null

  return (
    <>
      {/* Overlay avec animation */}
      <div
        className="fixed inset-0 bg-white bg-opacity-95 z-50 flex items-center justify-center p-4 modal-overlay cursor-pointer"
        onClick={onClose}
      >
        {/* Modale avec animation */}
        <div
          className="bg-white rounded-2xl shadow-2xl max-w-4xl w-full max-h-[90vh] overflow-hidden modal-content flex flex-col cursor-default"
          onClick={(e) => e.stopPropagation()}
        >
          {/* Header de la modale - FIXÉ */}
          <div className="bg-gradient-to-r from-blue-800 to-blue-900 text-white p-6 rounded-t-2xl flex-shrink-0 sticky top-0 z-10 relative">
            <button
              onClick={onClose}
              className="absolute top-4 right-4 text-white hover:text-blue-200 transition-colors duration-300 text-2xl w-8 h-8 flex items-center justify-center rounded-full hover:bg-white hover:bg-opacity-20 cursor-pointer"
            >
              <i className="fas fa-times"></i>
            </button>
            <h2 className="text-3xl font-bold mb-2">Qui Sommes-Nous ?</h2>
            <p className="text-blue-100">Découvrez l'équipe derrière elCodeur Services</p>
          </div>

          {/* Contenu de la modale - SCROLLABLE */}
          <div className="p-8 overflow-y-auto flex-1">
            {/* Section principale */}
            <div className="mb-8">
              <div className="flex flex-col lg:flex-row items-center gap-8">
                {/* Image/Logo */}
                <div className="flex-shrink-0">
                  <div className="w-32 h-32 bg-gradient-to-br from-blue-100 to-blue-200 rounded-full flex items-center justify-center shadow-lg">
                    <img 
                      src="/logo.gif" 
                      alt="elCodeur Services" 
                      className="w-24 h-24 object-contain"
                    />
                  </div>
                </div>
                
                {/* Texte principal */}
                <div className="flex-1">
                  <h3 className="text-2xl font-bold text-gray-800 mb-4">Notre Mission</h3>
                  <p className="text-gray-600 leading-relaxed mb-4">
                    Chez <strong>elCodeur Services</strong>, nous sommes passionnés par la création de solutions numériques 
                    innovantes qui transforment les idées en réalité. Notre équipe d'experts dévoués travaille sans relâche 
                    pour offrir des services de développement web et mobile de haute qualité.
                  </p>
                  <p className="text-gray-600 leading-relaxed">
                    Nous croyons que chaque projet est unique et mérite une approche personnalisée. C'est pourquoi nous 
                    prenons le temps de comprendre vos besoins spécifiques pour créer des solutions sur mesure qui 
                    dépassent vos attentes.
                  </p>
                </div>
              </div>
            </div>

            {/* Nos valeurs */}
            <div className="mb-8">
              <h3 className="text-2xl font-bold text-gray-800 mb-6 text-center">Nos Valeurs</h3>
              <div className="grid md:grid-cols-3 gap-6">
                <div className="text-center p-6 bg-blue-50 rounded-xl hover:bg-blue-100 transition-colors duration-300">
                  <div className="w-16 h-16 bg-blue-600 rounded-full flex items-center justify-center mx-auto mb-4">
                    <i className="fas fa-lightbulb text-white text-2xl"></i>
                  </div>
                  <h4 className="font-bold text-gray-800 mb-2">Innovation</h4>
                  <p className="text-gray-600 text-sm">
                    Nous restons à la pointe des technologies pour offrir des solutions modernes et efficaces.
                  </p>
                </div>
                
                <div className="text-center p-6 bg-green-50 rounded-xl hover:bg-green-100 transition-colors duration-300">
                  <div className="w-16 h-16 bg-green-600 rounded-full flex items-center justify-center mx-auto mb-4">
                    <i className="fas fa-handshake text-white text-2xl"></i>
                  </div>
                  <h4 className="font-bold text-gray-800 mb-2">Partenariat</h4>
                  <p className="text-gray-600 text-sm">
                    Nous travaillons en étroite collaboration avec nos clients pour garantir leur succès.
                  </p>
                </div>
                
                <div className="text-center p-6 bg-purple-50 rounded-xl hover:bg-purple-100 transition-colors duration-300">
                  <div className="w-16 h-16 bg-purple-600 rounded-full flex items-center justify-center mx-auto mb-4">
                    <i className="fas fa-star text-white text-2xl"></i>
                  </div>
                  <h4 className="font-bold text-gray-800 mb-2">Excellence</h4>
                  <p className="text-gray-600 text-sm">
                    Nous nous engageons à livrer des produits de la plus haute qualité dans les délais convenus.
                  </p>
                </div>
              </div>
            </div>

            {/* Notre équipe */}
            <div className="mb-8">
              <h3 className="text-2xl font-bold text-gray-800 mb-6 text-center">Notre Équipe</h3>
              <div className="bg-gradient-to-r from-blue-50 to-purple-50 p-8 rounded-xl shadow-lg">
                <div className="text-center">
                  <div className="w-40 h-40 bg-gradient-to-br from-blue-600 to-purple-600 rounded-full flex items-center justify-center mx-auto mb-6 shadow-xl overflow-hidden">
                    <img
                      src="/dg.jpeg"
                      alt="TOMEKPA ELIE MAHOUAN - Fondateur & DG"
                      className="w-full h-full object-cover rounded-full"
                    />
                  </div>
                  <h4 className="text-2xl font-bold text-gray-800 mb-2">TOMEKPA ELIE MAHOUAN</h4>
                  <div className="mb-4">
                    <p className="text-blue-600 font-bold text-lg mb-1">Fondateur (D.G) & Développeur Principal</p>
                    <p className="text-purple-600 font-medium text-base">Informaticien & Expert en Solutions Numériques</p>
                  </div>

                  {/* Domaines d'expertise */}
                  <div className="mb-6">
                    <div className="flex flex-wrap justify-center gap-2 mb-4">
                      <span className="bg-blue-100 text-blue-800 px-3 py-1 rounded-full text-sm font-medium">
                        <i className="fas fa-mobile-alt mr-1"></i>Applications Mobiles
                      </span>
                      <span className="bg-green-100 text-green-800 px-3 py-1 rounded-full text-sm font-medium">
                        <i className="fas fa-globe mr-1"></i>e-Services
                      </span>
                      <span className="bg-red-100 text-red-800 px-3 py-1 rounded-full text-sm font-medium">
                        <i className="fas fa-shield-alt mr-1"></i>Cybersécurité
                      </span>
                      <span className="bg-purple-100 text-purple-800 px-3 py-1 rounded-full text-sm font-medium">
                        <i className="fas fa-network-wired mr-1"></i>Internet des Objets
                      </span>
                    </div>
                  </div>

                  {/* Description professionnelle */}
                  <div className="bg-white bg-opacity-70 p-6 rounded-lg">
                    <p className="text-gray-700 text-base leading-relaxed max-w-3xl mx-auto mb-4">
                      <strong>Visionnaire et expert technique</strong>, Elie combine une solide formation en informatique
                      avec une passion débordante pour l'innovation numérique. Fort de son expertise en développement
                      d'applications mobiles et de services en ligne, il maîtrise également les enjeux critiques de la
                      cybersécurité et les technologies émergentes de l'Internet des Objets.
                    </p>
                    <p className="text-gray-700 text-base leading-relaxed max-w-3xl mx-auto">
                      En tant que <strong>fondateur et directeur général</strong> d'elCodeur Services, il apporte son
                      expertise technique pointue et sa vision créative pour transformer vos idées les plus ambitieuses
                      en solutions numériques performantes, sécurisées et innovantes qui répondent aux défis
                      technologiques d'aujourd'hui et de demain.
                    </p>
                  </div>
                </div>
              </div>
            </div>

            {/* Call to action */}
            <div className="text-center bg-gradient-to-r from-blue-800 to-blue-900 text-white p-6 rounded-xl">
              <h3 className="text-xl font-bold mb-3">Prêt à collaborer avec nous ?</h3>
              <p className="mb-4 text-blue-100">
                Contactez-nous dès aujourd'hui pour discuter de votre projet !
              </p>
              <div className="flex flex-wrap justify-center gap-4">
                <a 
                  href="tel:+22505567504067" 
                  className="bg-white text-blue-800 px-6 py-2 rounded-full font-medium hover:bg-blue-50 transition-colors duration-300 no-underline"
                >
                  <i className="fas fa-phone mr-2"></i>Nous appeler
                </a>
                <a 
                  href="mailto:<EMAIL>" 
                  className="bg-blue-700 text-white px-6 py-2 rounded-full font-medium hover:bg-blue-600 transition-colors duration-300 no-underline"
                >
                  <i className="fas fa-envelope mr-2"></i>Nous écrire
                </a>
              </div>
            </div>
          </div>
        </div>
      </div>
    </>
  )
}

export default AboutModal
