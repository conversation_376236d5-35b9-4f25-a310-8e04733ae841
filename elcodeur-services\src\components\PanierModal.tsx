import React, { useState } from 'react'

interface PanierItem {
  id: string
  title: string
  price: number
  quantity: number
  description: string
}

interface PanierModalProps {
  isOpen: boolean
  onClose: () => void
  items: PanierItem[]
  totalAmount: number
  onClearPanier: () => void
}

const PanierModal: React.FC<PanierModalProps> = ({ isOpen, onClose, items, totalAmount, onClearPanier }) => {
  const [formData, setFormData] = useState({
    nom: '',
    email: '',
    telephone: '',
    entreprise: '',
    adresse: '',
    commentaires: '',
    modePaiement: 'virement',
    urgence: 'normal'
  })

  const [isSubmitting, setIsSubmitting] = useState(false)
  const [isSuccess, setIsSuccess] = useState(false)

  // Format du prix en FCFA
  const formatPrice = (price: number) => {
    return new Intl.NumberFormat('fr-FR').format(price) + ' FCFA'
  }

  // Calcul des taxes (18% TVA)
  const tva = totalAmount * 0.18
  const totalTTC = totalAmount + tva

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value } = e.target
    setFormData(prev => ({ ...prev, [name]: value }))
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setIsSubmitting(true)

    // Simulation d'envoi de commande
    await new Promise(resolve => setTimeout(resolve, 2000))

    setIsSubmitting(false)
    setIsSuccess(true)

    // Réinitialiser après 3 secondes
    setTimeout(() => {
      setIsSuccess(false)
      onClearPanier()
      onClose()
      setFormData({
        nom: '',
        email: '',
        telephone: '',
        entreprise: '',
        adresse: '',
        commentaires: '',
        modePaiement: 'virement',
        urgence: 'normal'
      })
    }, 3000)
  }

  if (!isOpen) return null

  return (
    <div 
      className="fixed inset-0 bg-white bg-opacity-95 z-50 flex items-center justify-center p-4 cursor-pointer"
      onClick={onClose}
    >
      <div 
        className="bg-white rounded-2xl shadow-2xl w-full max-w-4xl max-h-[90vh] overflow-hidden cursor-default"
        onClick={(e) => e.stopPropagation()}
      >
        {/* En-tête */}
        <div className="bg-gradient-to-r from-green-600 to-blue-600 p-6 text-white">
          <div className="flex items-center justify-between">
            <div>
              <h2 className="text-2xl font-bold mb-2">
                <i className="fas fa-shopping-cart mr-3"></i>
                Finaliser votre commande
              </h2>
              <p className="text-green-100">Récapitulatif et informations de livraison</p>
            </div>
            <button
              onClick={onClose}
              className="p-2 hover:bg-white hover:bg-opacity-20 rounded-full transition-all duration-300 cursor-pointer"
            >
              <i className="fas fa-times text-2xl"></i>
            </button>
          </div>
        </div>

        {/* Contenu scrollable */}
        <div className="p-6 overflow-y-auto max-h-[calc(90vh-120px)]">
          {isSuccess ? (
            // Message de succès
            <div className="text-center py-12">
              <div className="mb-6">
                <i className="fas fa-check-circle text-6xl text-green-500"></i>
              </div>
              <h3 className="text-2xl font-bold text-gray-800 mb-4">Commande envoyée avec succès !</h3>
              <p className="text-gray-600 mb-4">
                Nous avons bien reçu votre commande. Notre équipe vous contactera dans les plus brefs délais.
              </p>
              <div className="bg-green-50 border border-green-200 rounded-lg p-4 inline-block">
                <p className="text-green-800 font-medium">
                  <i className="fas fa-envelope mr-2"></i>
                  Un email de confirmation vous sera envoyé à {formData.email}
                </p>
              </div>
            </div>
          ) : (
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
              
              {/* Colonne gauche - Récapitulatif commande */}
              <div>
                <h3 className="text-xl font-bold text-gray-800 mb-4 flex items-center">
                  <i className="fas fa-list-ul mr-3 text-blue-600"></i>
                  Récapitulatif de la commande
                </h3>
                
                <div className="bg-gray-50 rounded-xl p-4 mb-6">
                  {items.map((item) => (
                    <div key={item.id} className="flex justify-between items-center py-3 border-b border-gray-200 last:border-b-0">
                      <div className="flex-1">
                        <h4 className="font-medium text-gray-800">{item.title}</h4>
                        <p className="text-sm text-gray-600">Quantité: {item.quantity}</p>
                      </div>
                      <span className="font-bold text-blue-800">
                        {formatPrice(item.price * item.quantity)}
                      </span>
                    </div>
                  ))}
                </div>

                {/* Calculs */}
                <div className="bg-blue-50 rounded-xl p-4">
                  <div className="space-y-2">
                    <div className="flex justify-between">
                      <span className="text-gray-700">Sous-total HT:</span>
                      <span className="font-medium">{formatPrice(totalAmount)}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-700">TVA (18%):</span>
                      <span className="font-medium">{formatPrice(tva)}</span>
                    </div>
                    <div className="border-t border-blue-200 pt-2">
                      <div className="flex justify-between">
                        <span className="text-lg font-bold text-gray-800">Total TTC:</span>
                        <span className="text-lg font-bold text-blue-800">{formatPrice(totalTTC)}</span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              {/* Colonne droite - Formulaire */}
              <div>
                <h3 className="text-xl font-bold text-gray-800 mb-4 flex items-center">
                  <i className="fas fa-user mr-3 text-green-600"></i>
                  Informations de commande
                </h3>

                <form onSubmit={handleSubmit} className="space-y-4">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        Nom complet *
                      </label>
                      <input
                        type="text"
                        name="nom"
                        value={formData.nom}
                        onChange={handleInputChange}
                        required
                        className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                        placeholder="Votre nom complet"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        Email *
                      </label>
                      <input
                        type="email"
                        name="email"
                        value={formData.email}
                        onChange={handleInputChange}
                        required
                        className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                        placeholder="<EMAIL>"
                      />
                    </div>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        Téléphone *
                      </label>
                      <input
                        type="tel"
                        name="telephone"
                        value={formData.telephone}
                        onChange={handleInputChange}
                        required
                        className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                        placeholder="+225 XX XX XX XX"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        Entreprise
                      </label>
                      <input
                        type="text"
                        name="entreprise"
                        value={formData.entreprise}
                        onChange={handleInputChange}
                        className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                        placeholder="Nom de votre entreprise"
                      />
                    </div>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Adresse
                    </label>
                    <input
                      type="text"
                      name="adresse"
                      value={formData.adresse}
                      onChange={handleInputChange}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                      placeholder="Votre adresse complète"
                    />
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        Mode de paiement
                      </label>
                      <select
                        name="modePaiement"
                        value={formData.modePaiement}
                        onChange={handleInputChange}
                        className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                      >
                        <option value="virement">Virement bancaire</option>
                        <option value="mobile">Mobile Money</option>
                        <option value="especes">Espèces</option>
                        <option value="cheque">Chèque</option>
                      </select>
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        Urgence
                      </label>
                      <select
                        name="urgence"
                        value={formData.urgence}
                        onChange={handleInputChange}
                        className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                      >
                        <option value="normal">Normal (2-4 semaines)</option>
                        <option value="urgent">Urgent (+50%)</option>
                        <option value="express">Express (+100%)</option>
                      </select>
                    </div>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Commentaires
                    </label>
                    <textarea
                      name="commentaires"
                      value={formData.commentaires}
                      onChange={handleInputChange}
                      rows={3}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                      placeholder="Informations supplémentaires sur votre projet..."
                    ></textarea>
                  </div>

                  <div className="flex gap-4 pt-4">
                    <button
                      type="button"
                      onClick={onClose}
                      className="flex-1 bg-gray-500 hover:bg-gray-600 text-white py-3 px-6 rounded-lg font-medium transition-all duration-300 cursor-pointer"
                    >
                      Annuler
                    </button>
                    <button
                      type="submit"
                      disabled={isSubmitting}
                      className="flex-1 bg-gradient-to-r from-green-600 to-blue-600 hover:from-green-700 hover:to-blue-700 text-white py-3 px-6 rounded-lg font-medium transition-all duration-300 cursor-pointer disabled:opacity-50"
                    >
                      {isSubmitting ? (
                        <>
                          <i className="fas fa-spinner fa-spin mr-2"></i>
                          Envoi en cours...
                        </>
                      ) : (
                        <>
                          <i className="fas fa-paper-plane mr-2"></i>
                          Envoyer la commande
                        </>
                      )}
                    </button>
                  </div>
                </form>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  )
}

export default PanierModal
