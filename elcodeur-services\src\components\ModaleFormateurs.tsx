import React, { useState } from 'react'
import NetworkAnimation from './NetworkAnimation'

interface Formateur {
  id: number
  nom: string
  specialite: string
  experience: string
  photo: string
  description: string
  formations: string[]
  certifications: string[]
  methodePedagogique: string
  tauxReussite: number
  etudiantsFormes: number
  note: number
  languesEnseignement: string[]
  disponibilite: string
}

interface ModaleFormateursProps {
  isOpen: boolean
  onClose: () => void
}

const ModaleFormateurs: React.FC<ModaleFormateursProps> = ({ isOpen, onClose }) => {
  const [isHovering, setIsHovering] = useState(false)
  const [selectedFormateur, setSelectedFormateur] = useState<Formateur | null>(null)

  const formateurs: Formateur[] = [
    {
      id: 1,
      nom: "Fatou TRAORE",
      specialite: "Infographie & Design Graphique",
      experience: "8+ ans",
      photo: "/formateur-fatou.jpg",
      description: "Experte en design graphique avec une passion pour l'enseignement. Fatou forme les futurs graphistes avec une approche pratique et créative, en s'appuyant sur des projets réels.",
      formations: ["Formation Infographie Complète", "Adobe Creative Suite", "Design Thinking", "Identité Visuelle"],
      certifications: ["Adobe Certified Expert", "Formateur Professionnel Certifié", "Design Thinking Facilitator"],
      methodePedagogique: "Apprentissage par projet avec accompagnement personnalisé",
      tauxReussite: 95,
      etudiantsFormes: 150,
      note: 4.9,
      languesEnseignement: ["Français", "Anglais"],
      disponibilite: "Temps plein"
    },
    {
      id: 2,
      nom: "TOMEKPA ELIE MAHOUAN",
      specialite: "Développement Web & Administration",
      experience: "10+ ans",
      photo: "/formateur-elie.jpg",
      description: "Fondateur d'elCodeur Services et développeur senior. Elie partage son expertise technique à travers des formations pratiques axées sur les technologies modernes et les bonnes pratiques.",
      formations: ["Administration de Site Web", "Développement Full-Stack", "Architecture Logicielle", "DevOps"],
      certifications: ["AWS Solutions Architect", "Google Cloud Professional", "Scrum Master Certified"],
      methodePedagogique: "Formation hands-on avec projets industriels",
      tauxReussite: 92,
      etudiantsFormes: 200,
      note: 4.8,
      languesEnseignement: ["Français", "Anglais", "Espagnol"],
      disponibilite: "Temps partiel"
    },
    {
      id: 3,
      nom: "Sarah KOUAME",
      specialite: "Personal Branding & CV Pro",
      experience: "6+ ans",
      photo: "/formateur-sarah.jpg",
      description: "Consultante en développement professionnel et experte en personal branding. Sarah aide les professionnels à optimiser leur présence en ligne et à décrocher l'emploi de leurs rêves.",
      formations: ["CV Pro & Personal Branding", "LinkedIn Optimization", "Techniques de Recherche d'Emploi", "Préparation aux Entretiens"],
      certifications: ["Certified Career Coach", "LinkedIn Learning Instructor", "Personal Branding Specialist"],
      methodePedagogique: "Coaching personnalisé avec suivi individuel",
      tauxReussite: 98,
      etudiantsFormes: 120,
      note: 5.0,
      languesEnseignement: ["Français", "Anglais"],
      disponibilite: "Temps plein"
    },
    {
      id: 4,
      nom: "Kofi ASANTE",
      specialite: "Développement Mobile & Cloud",
      experience: "7+ ans",
      photo: "/formateur-kofi.jpg",
      description: "Développeur mobile senior et expert cloud. Kofi forme les développeurs aux technologies mobiles modernes et aux architectures cloud scalables.",
      formations: ["Développement Mobile React Native", "Flutter Development", "Cloud Computing", "API Development"],
      certifications: ["Google Flutter Certified", "AWS Mobile Specialist", "React Native Expert"],
      methodePedagogique: "Projets collaboratifs avec mentorat technique",
      tauxReussite: 90,
      etudiantsFormes: 85,
      note: 4.7,
      languesEnseignement: ["Français", "Anglais", "Twi"],
      disponibilite: "Week-ends"
    },
    {
      id: 5,
      nom: "Aminata DIALLO",
      specialite: "UI/UX Design & Frontend",
      experience: "5+ ans",
      photo: "/formateur-aminata.jpg",
      description: "Designer UI/UX passionnée et développeuse frontend. Aminata enseigne les principes du design centré utilisateur et les technologies frontend modernes.",
      formations: ["UI/UX Design", "Frontend Development", "Design Systems", "User Research"],
      certifications: ["Google UX Design Certificate", "Figma Expert", "Frontend Masters Instructor"],
      methodePedagogique: "Design thinking avec prototypage rapide",
      tauxReussite: 94,
      etudiantsFormes: 110,
      note: 4.8,
      languesEnseignement: ["Français", "Anglais"],
      disponibilite: "Temps partiel"
    }
  ]

  const renderStars = (note: number) => {
    return Array.from({ length: 5 }, (_, i) => (
      <i
        key={i}
        className={`fas fa-star ${i < Math.floor(note) ? 'text-yellow-400' : 'text-gray-300'}`}
      ></i>
    ))
  }

  if (!isOpen) return null

  return (
    <div className="fixed inset-0 bg-gradient-to-br from-emerald-50 to-green-50 flex items-center justify-center z-50 p-4">
      {/* Animation de réseau en arrière-plan */}
      <NetworkAnimation isExploding={isHovering} />
      
      <div 
        className="bg-white bg-opacity-95 backdrop-blur-sm rounded-2xl max-w-7xl w-full max-h-[90vh] overflow-hidden shadow-2xl border border-emerald-200 relative z-10"
        onMouseEnter={() => setIsHovering(true)}
        onMouseLeave={() => setIsHovering(false)}
      >
        {/* En-tête fixe */}
        <div className="bg-gradient-to-r from-emerald-600 to-green-600 text-white p-6 flex justify-between items-center">
          <div>
            <h2 className="text-3xl font-bold mb-2 flex items-center">
              <i className="fas fa-chalkboard-teacher mr-3"></i>
              Nos Formateurs
            </h2>
            <p className="text-emerald-100">Des experts pédagogues pour votre réussite professionnelle</p>
          </div>
          <button
            onClick={onClose}
            className="text-white hover:text-gray-200 text-2xl font-bold w-10 h-10 flex items-center justify-center rounded-full hover:bg-white hover:bg-opacity-20 transition-all cursor-pointer"
          >
            ×
          </button>
        </div>

        {/* Contenu scrollable */}
        <div className="overflow-y-auto max-h-[calc(90vh-120px)] p-6">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {formateurs.map((formateur) => (
              <div key={formateur.id} className="bg-white border border-emerald-200 rounded-xl overflow-hidden hover:shadow-lg transition-all duration-300">
                <div className="bg-gradient-to-r from-emerald-500 to-green-500 text-white p-4">
                  <div className="flex items-center mb-4">
                    <div className="w-16 h-16 bg-white bg-opacity-20 rounded-full flex items-center justify-center text-white font-bold text-xl mr-4">
                      {formateur.nom.split(' ').map(n => n[0]).join('').slice(0, 2)}
                    </div>
                    <div>
                      <h3 className="text-lg font-bold">{formateur.nom}</h3>
                      <p className="text-emerald-100 text-sm">{formateur.specialite}</p>
                    </div>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-sm bg-white bg-opacity-20 px-2 py-1 rounded-full">
                      {formateur.experience}
                    </span>
                    <div className="flex items-center">
                      {renderStars(formateur.note)}
                      <span className="ml-1 text-sm">{formateur.note}</span>
                    </div>
                  </div>
                </div>

                <div className="p-4">
                  <div className="mb-4">
                    <div className="grid grid-cols-2 gap-2 text-sm">
                      <div className="bg-emerald-50 p-2 rounded">
                        <div className="font-bold text-emerald-600">{formateur.etudiantsFormes}</div>
                        <div className="text-gray-600 text-xs">Étudiants formés</div>
                      </div>
                      <div className="bg-green-50 p-2 rounded">
                        <div className="font-bold text-green-600">{formateur.tauxReussite}%</div>
                        <div className="text-gray-600 text-xs">Taux de réussite</div>
                      </div>
                    </div>
                  </div>

                  <div className="mb-4">
                    <h4 className="font-semibold text-gray-800 mb-2">Formations dispensées :</h4>
                    <ul className="space-y-1">
                      {formateur.formations.slice(0, 2).map((formation, index) => (
                        <li key={index} className="flex items-start text-sm">
                          <i className="fas fa-graduation-cap text-emerald-500 mt-1 mr-2 text-xs"></i>
                          <span className="text-gray-600">{formation}</span>
                        </li>
                      ))}
                      {formateur.formations.length > 2 && (
                        <li className="text-sm text-emerald-600">
                          +{formateur.formations.length - 2} autres formations
                        </li>
                      )}
                    </ul>
                  </div>

                  <div className="mb-4">
                    <p className="text-gray-600 text-sm line-clamp-3">
                      {formateur.description}
                    </p>
                  </div>

                  <div className="flex gap-2">
                    <button
                      onClick={() => setSelectedFormateur(formateur)}
                      className="flex-1 bg-emerald-600 hover:bg-emerald-700 text-white px-3 py-2 rounded-lg text-sm font-medium transition-colors cursor-pointer"
                    >
                      Voir profil
                    </button>
                    <button className="px-3 py-2 border border-gray-300 rounded-lg text-sm font-medium text-gray-700 hover:bg-gray-50 transition-colors cursor-pointer">
                      Contact
                    </button>
                  </div>
                </div>
              </div>
            ))}
          </div>

          {/* Statistiques formateurs */}
          <div className="mt-8 bg-gradient-to-r from-emerald-50 to-green-50 p-6 rounded-xl border border-emerald-200">
            <h3 className="text-xl font-bold text-gray-800 mb-4 text-center">Excellence pédagogique</h3>
            <div className="grid grid-cols-1 md:grid-cols-4 gap-4 text-center">
              <div className="bg-white p-4 rounded-lg shadow">
                <div className="text-2xl font-bold text-emerald-600">5</div>
                <div className="text-sm text-gray-600">Formateurs experts</div>
              </div>
              <div className="bg-white p-4 rounded-lg shadow">
                <div className="text-2xl font-bold text-green-600">665+</div>
                <div className="text-sm text-gray-600">Étudiants formés</div>
              </div>
              <div className="bg-white p-4 rounded-lg shadow">
                <div className="text-2xl font-bold text-emerald-600">94%</div>
                <div className="text-sm text-gray-600">Taux de réussite moyen</div>
              </div>
              <div className="bg-white p-4 rounded-lg shadow">
                <div className="text-2xl font-bold text-green-600">4.8/5</div>
                <div className="text-sm text-gray-600">Satisfaction moyenne</div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Modale de détail formateur */}
      {selectedFormateur && (
        <div className="fixed inset-0 bg-gradient-to-br from-emerald-100 to-green-100 flex items-center justify-center z-60 p-4">
          {/* Animation de réseau pour la modale de détails */}
          <NetworkAnimation isExploding={true} />

          <div className="bg-white bg-opacity-95 backdrop-blur-sm rounded-2xl max-w-3xl w-full max-h-[90vh] overflow-hidden shadow-2xl border border-emerald-200 relative z-10">
            <div className="bg-gradient-to-r from-emerald-600 to-green-600 text-white p-6 flex justify-between items-center">
              <div>
                <h3 className="text-2xl font-bold">{selectedFormateur.nom}</h3>
                <p className="text-emerald-100">{selectedFormateur.specialite}</p>
              </div>
              <button
                onClick={() => setSelectedFormateur(null)}
                className="text-white hover:text-gray-200 text-2xl font-bold w-10 h-10 flex items-center justify-center rounded-full hover:bg-white hover:bg-opacity-20 transition-all cursor-pointer"
              >
                ×
              </button>
            </div>

            <div className="overflow-y-auto max-h-[calc(90vh-120px)] p-6">
              <div className="space-y-6">
                <div>
                  <h4 className="text-lg font-bold text-gray-800 mb-3">Profil du formateur</h4>
                  <p className="text-gray-700">{selectedFormateur.description}</p>
                </div>

                <div>
                  <h4 className="text-lg font-bold text-gray-800 mb-3">Méthode pédagogique</h4>
                  <p className="text-gray-700 bg-emerald-50 p-3 rounded-lg">
                    <i className="fas fa-lightbulb text-emerald-600 mr-2"></i>
                    {selectedFormateur.methodePedagogique}
                  </p>
                </div>

                <div>
                  <h4 className="text-lg font-bold text-gray-800 mb-3">Formations dispensées</h4>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-2">
                    {selectedFormateur.formations.map((formation, index) => (
                      <div key={index} className="bg-emerald-100 text-emerald-800 px-3 py-2 rounded-lg text-sm font-medium">
                        <i className="fas fa-graduation-cap mr-2"></i>
                        {formation}
                      </div>
                    ))}
                  </div>
                </div>

                <div>
                  <h4 className="text-lg font-bold text-gray-800 mb-3">Certifications</h4>
                  <div className="flex flex-wrap gap-2">
                    {selectedFormateur.certifications.map((certification, index) => (
                      <span key={index} className="bg-yellow-100 text-yellow-800 px-3 py-1 rounded-full text-sm font-medium">
                        <i className="fas fa-certificate mr-1"></i>
                        {certification}
                      </span>
                    ))}
                  </div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div className="bg-emerald-50 p-4 rounded-lg text-center">
                    <div className="text-2xl font-bold text-emerald-600">{selectedFormateur.etudiantsFormes}</div>
                    <div className="text-sm text-gray-600">Étudiants formés</div>
                  </div>
                  <div className="bg-green-50 p-4 rounded-lg text-center">
                    <div className="text-2xl font-bold text-green-600">{selectedFormateur.tauxReussite}%</div>
                    <div className="text-sm text-gray-600">Taux de réussite</div>
                  </div>
                  <div className="bg-yellow-50 p-4 rounded-lg text-center">
                    <div className="text-2xl font-bold text-yellow-600">{selectedFormateur.note}/5</div>
                    <div className="text-sm text-gray-600">Note moyenne</div>
                  </div>
                </div>

                <div>
                  <h4 className="text-lg font-bold text-gray-800 mb-3">Informations pratiques</h4>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <span className="font-medium text-gray-600">Disponibilité :</span>
                      <span className="ml-2 text-gray-800">{selectedFormateur.disponibilite}</span>
                    </div>
                    <div>
                      <span className="font-medium text-gray-600">Langues :</span>
                      <span className="ml-2 text-gray-800">{selectedFormateur.languesEnseignement.join(', ')}</span>
                    </div>
                  </div>
                </div>

                <div className="flex gap-4 justify-center">
                  <button
                    onClick={() => {
                      const contactSection = document.querySelector('[data-section="contact-form"]');
                      if (contactSection) {
                        contactSection.scrollIntoView({ behavior: 'smooth' });
                        setSelectedFormateur(null);
                        onClose();
                      }
                    }}
                    className="bg-emerald-600 hover:bg-emerald-700 text-white px-6 py-3 rounded-lg font-medium transition-colors cursor-pointer"
                  >
                    <i className="fas fa-envelope mr-2"></i>
                    Contacter
                  </button>
                  <button
                    onClick={() => setSelectedFormateur(null)}
                    className="bg-gray-500 hover:bg-gray-600 text-white px-6 py-3 rounded-lg font-medium transition-colors cursor-pointer"
                  >
                    Fermer
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}

export default ModaleFormateurs
