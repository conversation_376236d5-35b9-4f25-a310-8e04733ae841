import React, { useState } from 'react'

const ContactForm: React.FC = () => {
  const [formData, setFormData] = useState({
    nom: '',
    email: '',
    telephone: '',
    typeProjet: 'site-vitrine',
    message: '',
    fonctionnalites: [] as string[],
    budget: '200-300'
  })

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value } = e.target
    setFormData(prev => ({ ...prev, [name]: value }))
  }

  const handleCheckboxChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { value, checked } = e.target
    setFormData(prev => ({
      ...prev,
      fonctionnalites: checked 
        ? [...prev.fonctionnalites, value]
        : prev.fonctionnalites.filter(f => f !== value)
    }))
  }

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    console.log('Form submitted:', formData)
    // Handle form submission here
  }

  const fonctionnalitesList = [
    'Formulaire de contact',
    'Blog/Actualités',
    'Espace membre',
    'Paiement en ligne',
    'Multilangue',
    'Réservation/RDV',
    'Galerie photos',
    'Intégration réseaux sociaux',
    'Chat en ligne'
  ]

  return (
    <div className="bg-gray-100 rounded-lg p-8" data-section="contact-form">
      <h3 className="text-2xl font-bold mb-6 text-gray-800">Demande de devis pour votre projet web</h3>
      <form onSubmit={handleSubmit}>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
          <div>
            <label htmlFor="nom" className="block text-gray-700 font-medium mb-2">Nom complet</label>
            <input
              type="text"
              id="nom"
              name="nom"
              value={formData.nom}
              onChange={handleInputChange}
              className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
              required
            />
          </div>
          <div>
            <label htmlFor="email" className="block text-gray-700 font-medium mb-2">Email</label>
            <input
              type="email"
              id="email"
              name="email"
              value={formData.email}
              onChange={handleInputChange}
              className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
              required
            />
          </div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
          <div>
            <label htmlFor="telephone" className="block text-gray-700 font-medium mb-2">Téléphone</label>
            <input
              type="tel"
              id="telephone"
              name="telephone"
              value={formData.telephone}
              onChange={handleInputChange}
              className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
              required
            />
          </div>
          <div>
            <label htmlFor="typeProjet" className="block text-gray-700 font-medium mb-2">Type de projet</label>
            <select
              id="typeProjet"
              name="typeProjet"
              value={formData.typeProjet}
              onChange={handleInputChange}
              className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
              required
            >
              <option value="site-vitrine">Site Vitrine</option>
              <option value="e-commerce">Site E-commerce</option>
              <option value="application-web">Application Web</option>
              <option value="refonte-site">Refonte de site existant</option>
              <option value="autre">Autre (préciser)</option>
            </select>
          </div>
        </div>

        <div className="mb-6">
          <label htmlFor="message" className="block text-gray-700 font-medium mb-2">Description du projet</label>
          <textarea
            id="message"
            name="message"
            rows={4}
            value={formData.message}
            onChange={handleInputChange}
            className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
            placeholder="Décrivez votre projet, vos objectifs, fonctionnalités souhaitées, etc."
            required
          />
        </div>

        <div className="mb-6">
          <label className="block text-gray-700 font-medium mb-2">Fonctionnalités souhaitées</label>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            {fonctionnalitesList.map((fonctionnalite, index) => (
              <label key={index} className="flex items-center">
                <input
                  type="checkbox"
                  value={fonctionnalite}
                  onChange={handleCheckboxChange}
                  className="mr-2"
                />
                <span>{fonctionnalite}</span>
              </label>
            ))}
          </div>
        </div>

        <div className="mb-6">
          <label className="block text-gray-700 font-medium mb-2">Budget estimé</label>
          <div className="flex flex-wrap gap-4">
            {[
              { value: '200-300', label: '200 000 - 300 000 FCFA' },
              { value: '300-500', label: '300 000 - 500 000 FCFA' },
              { value: '500-plus', label: 'Plus de 500 000 FCFA' },
              { value: 'flexible', label: 'Flexible / À discuter' }
            ].map((budget, index) => (
              <label key={index} className="flex items-center">
                <input
                  type="radio"
                  name="budget"
                  value={budget.value}
                  checked={formData.budget === budget.value}
                  onChange={handleInputChange}
                  className="mr-2"
                />
                <span>{budget.label}</span>
              </label>
            ))}
          </div>
        </div>

        <button
          type="submit"
          className="bg-blue-800 hover:bg-blue-900 text-white px-6 py-3 rounded-full font-medium transition-all rounded-button cursor-pointer whitespace-nowrap"
        >
          <i className="fas fa-paper-plane mr-2"></i> Envoyer ma demande
        </button>
      </form>
    </div>
  )
}

export default ContactForm
