import React, { useEffect, useRef, useState } from 'react'

interface Node {
  x: number
  y: number
  vx: number
  vy: number
  radius: number
  connections: number[]
}

interface NetworkAnimationProps {
  isExploding: boolean
}

const NetworkAnimation: React.FC<NetworkAnimationProps> = ({ isExploding }) => {
  const canvasRef = useRef<HTMLCanvasElement>(null)
  const animationRef = useRef<number>()
  const nodesRef = useRef<Node[]>([])
  const [dimensions, setDimensions] = useState({ width: 0, height: 0 })

  useEffect(() => {
    const updateDimensions = () => {
      if (canvasRef.current) {
        const rect = canvasRef.current.getBoundingClientRect()
        setDimensions({ width: rect.width, height: rect.height })
      }
    }

    updateDimensions()
    window.addEventListener('resize', updateDimensions)
    return () => window.removeEventListener('resize', updateDimensions)
  }, [])

  useEffect(() => {
    if (!dimensions.width || !dimensions.height) return

    const canvas = canvasRef.current
    if (!canvas) return

    const ctx = canvas.getContext('2d')
    if (!ctx) return

    canvas.width = dimensions.width
    canvas.height = dimensions.height

    // Initialiser les nœuds
    const nodeCount = 25
    const nodes: Node[] = []

    for (let i = 0; i < nodeCount; i++) {
      nodes.push({
        x: Math.random() * dimensions.width,
        y: Math.random() * dimensions.height,
        vx: (Math.random() - 0.5) * 0.5,
        vy: (Math.random() - 0.5) * 0.5,
        radius: Math.random() * 3 + 2,
        connections: []
      })
    }

    // Créer les connexions
    nodes.forEach((node, i) => {
      nodes.forEach((otherNode, j) => {
        if (i !== j) {
          const distance = Math.sqrt(
            Math.pow(node.x - otherNode.x, 2) + Math.pow(node.y - otherNode.y, 2)
          )
          if (distance < 120 && node.connections.length < 3) {
            node.connections.push(j)
          }
        }
      })
    })

    nodesRef.current = nodes

    const animate = () => {
      ctx.clearRect(0, 0, dimensions.width, dimensions.height)

      // Mettre à jour les positions des nœuds
      nodesRef.current.forEach(node => {
        if (isExploding) {
          // Animation d'explosion
          node.vx *= 1.1
          node.vy *= 1.1
          node.x += node.vx * 5
          node.y += node.vy * 5
          node.radius *= 1.02
        } else {
          // Animation normale
          node.x += node.vx
          node.y += node.vy

          // Rebond sur les bords
          if (node.x <= 0 || node.x >= dimensions.width) node.vx *= -1
          if (node.y <= 0 || node.y >= dimensions.height) node.vy *= -1

          // Garder dans les limites
          node.x = Math.max(0, Math.min(dimensions.width, node.x))
          node.y = Math.max(0, Math.min(dimensions.height, node.y))
        }
      })

      // Dessiner les connexions
      ctx.strokeStyle = isExploding ? 'rgba(59, 130, 246, 0.9)' : 'rgba(59, 130, 246, 0.2)'
      ctx.lineWidth = isExploding ? 3 : 1

      nodesRef.current.forEach((node, i) => {
        node.connections.forEach(connectionIndex => {
          const connectedNode = nodesRef.current[connectionIndex]
          if (connectedNode) {
            const distance = Math.sqrt(
              Math.pow(node.x - connectedNode.x, 2) + Math.pow(node.y - connectedNode.y, 2)
            )

            if (distance < (isExploding ? 300 : 120)) {
              ctx.beginPath()
              ctx.moveTo(node.x, node.y)
              ctx.lineTo(connectedNode.x, connectedNode.y)
              ctx.stroke()
            }
          }
        })
      })

      // Dessiner les nœuds
      nodesRef.current.forEach(node => {
        ctx.beginPath()
        ctx.arc(node.x, node.y, node.radius, 0, Math.PI * 2)
        
        if (isExploding) {
          const gradient = ctx.createRadialGradient(node.x, node.y, 0, node.x, node.y, node.radius)
          gradient.addColorStop(0, 'rgba(59, 130, 246, 1)')
          gradient.addColorStop(0.5, 'rgba(147, 51, 234, 0.9)')
          gradient.addColorStop(1, 'rgba(59, 130, 246, 0.4)')
          ctx.fillStyle = gradient
        } else {
          ctx.fillStyle = 'rgba(59, 130, 246, 0.4)'
        }
        
        ctx.fill()

        // Effet de pulsation pour l'explosion
        if (isExploding) {
          // Cercle de pulsation principal
          ctx.beginPath()
          ctx.arc(node.x, node.y, node.radius * 1.8, 0, Math.PI * 2)
          ctx.fillStyle = 'rgba(147, 51, 234, 0.3)'
          ctx.fill()

          // Cercle de pulsation secondaire
          ctx.beginPath()
          ctx.arc(node.x, node.y, node.radius * 2.5, 0, Math.PI * 2)
          ctx.fillStyle = 'rgba(59, 130, 246, 0.1)'
          ctx.fill()

          // Particules qui s'échappent
          for (let i = 0; i < 6; i++) {
            const angle = (i * Math.PI * 2) / 6
            const distance = node.radius * 3
            const particleX = node.x + Math.cos(angle) * distance
            const particleY = node.y + Math.sin(angle) * distance

            ctx.beginPath()
            ctx.arc(particleX, particleY, 1, 0, Math.PI * 2)
            ctx.fillStyle = 'rgba(147, 51, 234, 0.6)'
            ctx.fill()
          }
        }
      })

      animationRef.current = requestAnimationFrame(animate)
    }

    animate()

    return () => {
      if (animationRef.current) {
        cancelAnimationFrame(animationRef.current)
      }
    }
  }, [dimensions, isExploding])

  // Réinitialiser les nœuds quand l'explosion s'arrête
  useEffect(() => {
    if (!isExploding && dimensions.width && dimensions.height) {
      const nodeCount = 25
      const nodes: Node[] = []

      for (let i = 0; i < nodeCount; i++) {
        nodes.push({
          x: Math.random() * dimensions.width,
          y: Math.random() * dimensions.height,
          vx: (Math.random() - 0.5) * 0.5,
          vy: (Math.random() - 0.5) * 0.5,
          radius: Math.random() * 3 + 2,
          connections: []
        })
      }

      // Recréer les connexions
      nodes.forEach((node, i) => {
        nodes.forEach((otherNode, j) => {
          if (i !== j) {
            const distance = Math.sqrt(
              Math.pow(node.x - otherNode.x, 2) + Math.pow(node.y - otherNode.y, 2)
            )
            if (distance < 120 && node.connections.length < 3) {
              node.connections.push(j)
            }
          }
        })
      })

      nodesRef.current = nodes
    }
  }, [isExploding, dimensions])

  return (
    <canvas
      ref={canvasRef}
      className="absolute inset-0 w-full h-full pointer-events-none"
      style={{ zIndex: 1 }}
    />
  )
}

export default NetworkAnimation
