import React, { useState } from 'react'
import ListeRealisations from './ListeRealisations'
import DetailRealisation from './DetailRealisation'
import { Swiper, SwiperSlide } from 'swiper/react'
import { Navigation, Pagination, Autoplay } from 'swiper/modules'

interface Projet {
  id: number
  image: string
  alt: string
  category: string
  tech: string
  title: string
  description: string
  dateRealisation: string
  client?: string
  statut: 'Terminé' | 'En cours' | 'En maintenance'
  descriptionComplete: string
  fonctionnalites: string[]
  technologies: string[]
  dureeProjet: string
  equipe: string[]
  defis: string[]
  resultats: string[]
  lienDemo?: string
  lienGithub?: string
}

const Portfolio: React.FC = () => {
  const [isListeRealisationsOpen, setIsListeRealisationsOpen] = useState(false)
  const [selectedProjet, setSelectedProjet] = useState<Projet | null>(null)
  const [isDetailOpen, setIsDetailOpen] = useState(false)
  const projects = [
    {
      id: 1,
      image: '/1.jpg',
      alt: 'E-commerce - Mode Africaine',
      category: 'E-commerce',
      tech: 'WordPress / WooCommerce',
      title: 'E-commerce - Mode Africaine',
      description: 'Plateforme e-commerce spécialisée dans la mode africaine avec catalogue riche et paiement sécurisé.',
      dateRealisation: 'Mars 2024',
      client: 'African Fashion Store',
      statut: 'Terminé' as const,
      descriptionComplete: 'Développement d\'une plateforme e-commerce dédiée à la mode africaine, mettant en valeur l\'artisanat local et les créateurs africains. Le site propose une expérience d\'achat immersive avec des fonctionnalités avancées de personnalisation et un système de paiement adapté aux marchés africains.',
      fonctionnalites: [
        'Catalogue produits avec filtres par origine',
        'Système de personnalisation des vêtements',
        'Paiement mobile money intégré',
        'Profils des créateurs et artisans',
        'Système de tailles africaines',
        'Livraison internationale',
        'Programme de fidélité culturel',
        'Blog mode africaine'
      ],
      technologies: ['WordPress', 'WooCommerce', 'PHP', 'MySQL', 'JavaScript', 'CSS3', 'Mobile Money API'],
      dureeProjet: '4 mois',
      equipe: ['Développeur WordPress', 'Designer UI/UX', 'Expert e-commerce'],
      defis: [
        'Intégration des systèmes de paiement africains',
        'Gestion des tailles et mesures locales',
        'Optimisation pour connexions lentes',
        'Support multilingue (français, anglais, langues locales)'
      ],
      resultats: [
        'Augmentation des ventes de 200%',
        'Expansion dans 5 pays africains',
        '1000+ créateurs partenaires',
        'Temps de chargement optimisé pour l\'Afrique'
      ],
      lienDemo: 'https://african-fashion-demo.com'
    },
    {
      id: 2,
      image: '/2.jpg',
      alt: 'Site Vitrine - Cabinet Juridique',
      category: 'Site Vitrine',
      tech: 'HTML5 / CSS3 / JavaScript',
      title: 'Site Vitrine - Cabinet Juridique',
      description: 'Site vitrine professionnel pour cabinet d\'avocats avec prise de rendez-vous en ligne.',
      dateRealisation: 'Février 2024',
      client: 'Cabinet Juridique Excellence',
      statut: 'Terminé' as const,
      descriptionComplete: 'Création d\'un site vitrine élégant et professionnel pour un cabinet d\'avocats, mettant l\'accent sur la crédibilité et l\'expertise. Le site inclut une présentation des services juridiques, des profils d\'avocats, et un système de prise de rendez-vous en ligne.',
      fonctionnalites: [
        'Présentation des services juridiques',
        'Profils détaillés des avocats',
        'Prise de rendez-vous en ligne',
        'Blog juridique',
        'Formulaire de contact sécurisé',
        'Témoignages clients',
        'Actualités juridiques',
        'Optimisation SEO'
      ],
      technologies: ['HTML5', 'CSS3', 'JavaScript', 'PHP', 'MySQL', 'Bootstrap'],
      dureeProjet: '2 mois',
      equipe: ['Développeur Front-end', 'Designer UI/UX'],
      defis: [
        'Design professionnel et crédible',
        'Optimisation SEO pour le secteur juridique',
        'Conformité RGPD pour les données clients',
        'Interface intuitive pour tous âges'
      ],
      resultats: [
        'Augmentation des consultations de 180%',
        'Amélioration du référencement Google',
        'Réduction du temps de prise de RDV',
        'Image de marque renforcée'
      ]
    },
    {
      id: 3,
      image: '/3.jpg',
      alt: 'Application - Gestion Immobilière',
      category: 'Application Web',
      tech: 'React / Node.js / MongoDB',
      title: 'Application - Gestion Immobilière',
      description: 'Solution complète de gestion immobilière avec CRM intégré.',
      dateRealisation: 'Janvier 2024',
      client: 'Immobilier Plus',
      statut: 'Terminé' as const,
      descriptionComplete: 'Application web complète pour la gestion d\'agence immobilière.',
      fonctionnalites: ['CRM intégré', 'Gestion des biens', 'Suivi des clients'],
      technologies: ['React', 'Node.js', 'MongoDB', 'Express'],
      dureeProjet: '4 mois',
      equipe: ['Développeur Full-Stack', 'Designer'],
      defis: ['Intégration CRM', 'Interface complexe'],
      resultats: ['Productivité +150%', 'Satisfaction client 98%']
    },
    {
      id: 4,
      image: '/4.jpg',
      alt: 'Site Vitrine - Restaurant Gourmet',
      category: 'Site Vitrine',
      tech: 'WordPress / Elementor',
      title: 'Site Vitrine - Restaurant Gourmet',
      description: 'Site vitrine élégant pour restaurant avec menu interactif.',
      dateRealisation: 'Décembre 2023',
      client: 'Restaurant Le Gourmet',
      statut: 'Terminé' as const,
      descriptionComplete: 'Site vitrine moderne pour restaurant avec réservation en ligne.',
      fonctionnalites: ['Menu interactif', 'Réservation en ligne', 'Galerie photos'],
      technologies: ['WordPress', 'Elementor', 'PHP', 'JavaScript'],
      dureeProjet: '2 mois',
      equipe: ['Développeur WordPress', 'Designer'],
      defis: ['Design responsive', 'Optimisation SEO'],
      resultats: ['Réservations +300%', 'Visibilité web améliorée']
    },
    {
      id: 5,
      image: '/5.jpg',
      alt: 'E-commerce - Artisanat Local',
      category: 'E-commerce',
      tech: 'Shopify / Liquid',
      title: 'E-commerce - Artisanat Local',
      description: 'Boutique en ligne dédiée à l\'artisanat local africain.',
      dateRealisation: 'Novembre 2023',
      client: 'Artisans Africains',
      statut: 'Terminé' as const,
      descriptionComplete: 'E-commerce spécialisé dans l\'artisanat local africain.',
      fonctionnalites: ['Catalogue artisanal', 'Profils artisans', 'Paiement local'],
      technologies: ['Shopify', 'Liquid', 'JavaScript', 'Shopify API'],
      dureeProjet: '3 mois',
      equipe: ['Développeur Shopify', 'Designer'],
      defis: ['Intégration artisans', 'Paiement local'],
      resultats: ['200+ artisans partenaires', 'Ventes +180%']
    },
    {
      id: 6,
      image: '/6.jpg',
      alt: 'Application - Suivi Médical',
      category: 'Application Web',
      tech: 'Vue.js / Laravel / MySQL',
      title: 'Application - Suivi Médical',
      description: 'Plateforme de télémédecine avec consultations en ligne.',
      dateRealisation: 'Octobre 2023',
      client: 'Clinique Moderne',
      statut: 'Terminé' as const,
      descriptionComplete: 'Plateforme de télémédecine moderne pour consultations à distance.',
      fonctionnalites: ['Consultations vidéo', 'Dossiers médicaux', 'Prise de RDV'],
      technologies: ['Vue.js', 'Laravel', 'MySQL', 'WebRTC'],
      dureeProjet: '4 mois',
      equipe: ['Développeur Full-Stack', 'Expert sécurité'],
      defis: ['Conformité RGPD', 'Sécurité données médicales'],
      resultats: ['500+ consultations/mois', 'Temps attente réduit 70%']
    },
    // Section 2 du carousel - 6 projets supplémentaires
    {
      id: 7,
      image: '/search-image',
      alt: 'Site Vitrine - Spa & Wellness',
      category: 'Site Vitrine',
      tech: 'HTML5 / CSS3 / JavaScript',
      title: 'Site Vitrine - Spa & Wellness',
      description: 'Site vitrine élégant pour spa avec système de réservation.',
      dateRealisation: 'Septembre 2023',
      client: 'Spa Wellness',
      statut: 'Terminé' as const,
      descriptionComplete: 'Site vitrine moderne pour spa avec réservation en ligne.',
      fonctionnalites: ['Réservation en ligne', 'Galerie services', 'Témoignages'],
      technologies: ['HTML5', 'CSS3', 'JavaScript', 'PHP'],
      dureeProjet: '2 mois',
      equipe: ['Développeur Front-end', 'Designer'],
      defis: ['Design zen', 'Système réservation'],
      resultats: ['Réservations +250%', 'Satisfaction client 95%']
    },
    {
      id: 8,
      image: '/7.jpg',
      alt: 'E-commerce - Tech & Gadgets',
      category: 'E-commerce',
      tech: 'WooCommerce / WordPress',
      title: 'E-commerce - Tech & Gadgets',
      description: 'Boutique en ligne spécialisée dans la technologie.',
      dateRealisation: 'Août 2023',
      client: 'Tech Store',
      statut: 'Terminé' as const,
      descriptionComplete: 'E-commerce spécialisé dans les gadgets technologiques.',
      fonctionnalites: ['Catalogue tech', 'Comparateur produits', 'Avis clients'],
      technologies: ['WordPress', 'WooCommerce', 'PHP', 'MySQL'],
      dureeProjet: '3 mois',
      equipe: ['Développeur WordPress', 'Expert e-commerce'],
      defis: ['Catalogue complexe', 'Comparateur produits'],
      resultats: ['Ventes +300%', '5000+ produits référencés']
    },
    {
      id: 9,
      image: '/8.jpg',
      alt: 'Application - Inventory Pro',
      category: 'Application Web',
      tech: 'React / Express / MongoDB',
      title: 'Application - Inventory Pro',
      description: 'Application de gestion des stocks avancée.',
      dateRealisation: 'Juillet 2023',
      client: 'Warehouse Pro',
      statut: 'En maintenance' as const,
      descriptionComplete: 'Système avancé de gestion des stocks avec analytics.',
      fonctionnalites: ['Gestion stocks', 'Analytics', 'Alertes automatiques'],
      technologies: ['React', 'Express', 'MongoDB', 'Chart.js'],
      dureeProjet: '4 mois',
      equipe: ['Développeur Full-Stack', 'Analyste données'],
      defis: ['Analytics temps réel', 'Interface complexe'],
      resultats: ['Erreurs stock -80%', 'Efficacité +150%']
    },
    {
      id: 10,
      image: '/9.jpg',
      alt: 'Site Vitrine - Agence Marketing',
      category: 'Site Vitrine',
      tech: 'WordPress / Elementor',
      title: 'Site Vitrine - Agence Marketing',
      description: 'Site vitrine pour agence de marketing digital.',
      dateRealisation: 'Juin 2023',
      client: 'Digital Agency',
      statut: 'Terminé' as const,
      descriptionComplete: 'Site vitrine moderne pour agence marketing.',
      fonctionnalites: ['Portfolio projets', 'Blog marketing', 'Contact'],
      technologies: ['WordPress', 'Elementor', 'PHP', 'JavaScript'],
      dureeProjet: '2 mois',
      equipe: ['Développeur WordPress', 'Designer'],
      defis: ['Design créatif', 'Portfolio interactif'],
      resultats: ['Clients +200%', 'Visibilité améliorée']
    },
    {
      id: 11,
      image: '/10.jpg',
      alt: 'E-commerce - Mode & Accessoires',
      category: 'E-commerce',
      tech: 'Shopify / Liquid',
      title: 'E-commerce - Mode & Accessoires',
      description: 'Boutique en ligne de mode et accessoires.',
      dateRealisation: 'Mai 2023',
      client: 'Fashion Accessories',
      statut: 'Terminé' as const,
      descriptionComplete: 'E-commerce spécialisé mode et accessoires.',
      fonctionnalites: ['Catalogue mode', 'Wishlist', 'Programme fidélité'],
      technologies: ['Shopify', 'Liquid', 'JavaScript', 'Shopify API'],
      dureeProjet: '3 mois',
      equipe: ['Développeur Shopify', 'Designer mode'],
      defis: ['Design tendance', 'Catalogue varié'],
      resultats: ['Ventes +180%', 'Clients fidèles +120%']
    },
    {
      id: 12,
      image: '/11.jpg',
      alt: 'Application - Gestion Projet',
      category: 'Application Web',
      tech: 'Vue.js / Laravel / MySQL',
      title: 'Application - Gestion Projet',
      description: 'Application complète de gestion de projets.',
      dateRealisation: 'Avril 2023',
      client: 'Project Manager Pro',
      statut: 'En cours' as const,
      descriptionComplete: 'Application de gestion de projets avec collaboration.',
      fonctionnalites: ['Gestion tâches', 'Collaboration équipe', 'Rapports'],
      technologies: ['Vue.js', 'Laravel', 'MySQL', 'WebSocket'],
      dureeProjet: '5 mois',
      equipe: ['Développeur Full-Stack', 'Expert UX'],
      defis: ['Collaboration temps réel', 'Interface complexe'],
      resultats: ['Productivité +120%', 'Équipes satisfaites 90%']
    }
  ]

  // Fonction pour diviser les projets en groupes de 6
  const chunkArray = (array: typeof projects, size: number) => {
    const chunks = []
    for (let i = 0; i < array.length; i += size) {
      chunks.push(array.slice(i, i + size))
    }
    return chunks
  }

  const projectChunks = chunkArray(projects, 6)

  return (
    <div className="mb-16" data-section="portfolio">
      <div className="container mx-auto px-4">
        <h3 className="text-2xl font-bold mb-6 text-gray-800">Nos réalisations</h3>

        {/* Carousel Swiper avec sections de 6 éléments */}
        <div className="relative">
        <Swiper
          modules={[Navigation, Pagination, Autoplay]}
          spaceBetween={50}
          slidesPerView={1}
          slidesPerGroup={1}
          navigation={{
            nextEl: '.portfolio-next',
            prevEl: '.portfolio-prev',
          }}
          pagination={{
            clickable: true,
            dynamicBullets: true,
          }}
          autoplay={{
            delay: 8000,
            disableOnInteraction: false,
          }}
          loop={false}
          allowTouchMove={true}
          grabCursor={true}
          centeredSlides={true}
          watchOverflow={true}
          className="portfolio-swiper"
        >
          {projectChunks.map((chunk, chunkIndex) => (
            <SwiperSlide key={chunkIndex}>
              <div className="portfolio-grid">
                {chunk.map((project, index) => (
                  <div key={index} className="bg-white rounded-lg shadow-md overflow-hidden group h-full">
                    <div className="relative">
                      <img
                        src={project.image}
                        alt={project.alt}
                        className="w-full h-48 object-cover"
                      />
                      <div className="absolute inset-0 bg-gradient-to-t from-black/70 to-transparent opacity-0 group-hover:opacity-100 transition-opacity flex items-center justify-center">
                        <button
                          onClick={() => {
                            setSelectedProjet(project)
                            setIsDetailOpen(true)
                          }}
                          className="bg-white text-blue-800 px-4 py-2 rounded-full font-medium transform translate-y-4 group-hover:translate-y-0 transition-transform rounded-button cursor-pointer whitespace-nowrap"
                        >
                          <i className="fas fa-eye mr-2"></i> Voir le projet
                        </button>
                      </div>
                    </div>
                    <div className="p-6">
                      <div className="flex justify-between items-start mb-2">
                        <span className="bg-blue-100 text-blue-800 text-xs px-2 py-1 rounded-full">
                          {project.category}
                        </span>
                        <span className="text-xs text-gray-500">{project.tech}</span>
                      </div>
                      <h4 className="text-lg font-bold">{project.title}</h4>
                    </div>
                  </div>
                ))}
              </div>
            </SwiperSlide>
          ))}
        </Swiper>

        {/* Boutons de navigation personnalisés */}
          <div className="portfolio-prev absolute left-4 top-1/2 transform -translate-y-1/2 z-20 bg-blue-800 hover:bg-blue-900 text-white w-12 h-12 rounded-full flex items-center justify-center cursor-pointer transition-all shadow-lg">
            <i className="fas fa-chevron-left"></i>
          </div>
          <div className="portfolio-next absolute right-4 top-1/2 transform -translate-y-1/2 z-20 bg-blue-800 hover:bg-blue-900 text-white w-12 h-12 rounded-full flex items-center justify-center cursor-pointer transition-all shadow-lg">
            <i className="fas fa-chevron-right"></i>
          </div>
        </div>

        <div className="text-center mt-8">
          <button
            onClick={() => setIsListeRealisationsOpen(true)}
            className="bg-blue-800 hover:bg-blue-900 text-white px-6 py-3 rounded-full font-medium transition-all rounded-button cursor-pointer whitespace-nowrap"
          >
            <i className="fas fa-plus-circle mr-2"></i> Voir plus de projets
          </button>
        </div>
      </div>

      {/* Modale Liste des Réalisations */}
      <ListeRealisations
        isOpen={isListeRealisationsOpen}
        onClose={() => setIsListeRealisationsOpen(false)}
      />

      {/* Modale Détail Réalisation */}
      <DetailRealisation
        isOpen={isDetailOpen}
        onClose={() => {
          setIsDetailOpen(false)
          setSelectedProjet(null)
        }}
        projet={selectedProjet}
      />
    </div>
  )
}

export default Portfolio
