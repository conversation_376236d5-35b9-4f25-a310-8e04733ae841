import React, { useState, useEffect } from 'react'

const SectionHeurDate: React.FC = () => {
  const [currentTime, setCurrentTime] = useState(new Date())
  const [selectedDate, setSelectedDate] = useState(new Date())
  const [currentMonth, setCurrentMonth] = useState(new Date())

  // Mise à jour de l'heure toutes les secondes
  useEffect(() => {
    const timer = setInterval(() => {
      setCurrentTime(new Date())
    }, 1000)

    return () => clearInterval(timer)
  }, [])

  // Fonction pour obtenir les jours du mois
  const getDaysInMonth = (date: Date) => {
    const year = date.getFullYear()
    const month = date.getMonth()
    const firstDay = new Date(year, month, 1)
    const lastDay = new Date(year, month + 1, 0)
    const daysInMonth = lastDay.getDate()
    const startingDayOfWeek = firstDay.getDay()

    const days = []
    
    // Jo<PERSON> vides au début
    for (let i = 0; i < startingDayOfWeek; i++) {
      days.push(null)
    }
    
    // Jours du mois
    for (let day = 1; day <= daysInMonth; day++) {
      days.push(new Date(year, month, day))
    }
    
    return days
  }

  // Navigation du calendrier
  const navigateMonth = (direction: 'prev' | 'next') => {
    setCurrentMonth(prev => {
      const newMonth = new Date(prev)
      if (direction === 'prev') {
        newMonth.setMonth(prev.getMonth() - 1)
      } else {
        newMonth.setMonth(prev.getMonth() + 1)
      }
      return newMonth
    })
  }

  // Formatage de l'heure
  const formatTime = (date: Date) => {
    return date.toLocaleTimeString('fr-FR', {
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit'
    })
  }

  // Formatage de la date
  const formatDate = (date: Date) => {
    return date.toLocaleDateString('fr-FR', {
      weekday: 'long',
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    })
  }

  // Noms des jours de la semaine
  const dayNames = ['Dim', 'Lun', 'Mar', 'Mer', 'Jeu', 'Ven', 'Sam']
  
  // Noms des mois
  const monthNames = [
    'Janvier', 'Février', 'Mars', 'Avril', 'Mai', 'Juin',
    'Juillet', 'Août', 'Septembre', 'Octobre', 'Novembre', 'Décembre'
  ]

  const days = getDaysInMonth(currentMonth)

  return (
    <div className="py-16 bg-gradient-to-br from-gray-50 to-blue-50">
      <div className="container mx-auto px-4">
        <div className="text-center mb-12">
          <h2 className="text-3xl font-bold text-gray-800 mb-4">
            Calendrier & Horloge
          </h2>
          <p className="text-gray-600 max-w-2xl mx-auto">
            Restez organisé avec notre calendrier interactif et suivez l'heure en temps réel
          </p>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 max-w-6xl mx-auto">
          
          {/* Carte Calendrier */}
          <div className="bg-white rounded-2xl shadow-xl overflow-hidden border border-gray-100">
            <div className="bg-gradient-to-r from-red-800 to-red-600 p-6 text-white">
              <div className="flex items-center justify-between">
                <button 
                  onClick={() => navigateMonth('prev')}
                  className="p-2 hover:bg-white hover:bg-opacity-20 rounded-full transition-all duration-300"
                >
                  <i className="fas fa-chevron-left text-xl"></i>
                </button>
                
                <h3 className="text-2xl font-bold text-center">
                  {monthNames[currentMonth.getMonth()]} {currentMonth.getFullYear()}
                </h3>
                
                <button 
                  onClick={() => navigateMonth('next')}
                  className="p-2 hover:bg-white hover:bg-opacity-20 rounded-full transition-all duration-300"
                >
                  <i className="fas fa-chevron-right text-xl"></i>
                </button>
              </div>
            </div>

            <div className="p-6">
              {/* En-têtes des jours */}
              <div className="grid grid-cols-7 gap-1 mb-4">
                {dayNames.map((day) => (
                  <div key={day} className="text-center py-2 font-semibold text-gray-600 text-sm">
                    {day}
                  </div>
                ))}
              </div>

              {/* Grille du calendrier */}
              <div className="grid grid-cols-7 gap-1">
                {days.map((day, index) => (
                  <button
                    key={index}
                    onClick={() => day && setSelectedDate(day)}
                    className={`
                      h-10 w-10 rounded-lg text-sm font-medium transition-all duration-200
                      ${!day ? 'invisible' : ''}
                      ${day && day.toDateString() === new Date().toDateString() 
                        ? 'bg-blue-600 text-white shadow-lg' 
                        : day && day.toDateString() === selectedDate.toDateString()
                        ? 'bg-red-600 text-white shadow-lg'
                        : 'hover:bg-gray-100 text-gray-700'
                      }
                    `}
                    disabled={!day}
                  >
                    {day?.getDate()}
                  </button>
                ))}
              </div>

              {/* Date sélectionnée */}
              <div className="mt-6 p-4 bg-gradient-to-r from-red-50 to-blue-50 rounded-lg">
                <p className="text-sm text-gray-600 mb-1">Date sélectionnée :</p>
                <p className="font-semibold text-gray-800">
                  {formatDate(selectedDate)}
                </p>
              </div>
            </div>
          </div>

          {/* Carte Horloge */}
          <div className="bg-white rounded-2xl shadow-xl overflow-hidden border border-gray-100">
            <div className="bg-gradient-to-r from-blue-600 to-blue-800 p-6 text-white">
              <h3 className="text-2xl font-bold text-center flex items-center justify-center">
                <i className="fas fa-clock mr-3"></i>
                Horloge Temps Réel
              </h3>
            </div>

            <div className="p-8 text-center">
              {/* Affichage de l'heure */}
              <div className="mb-8">
                <div className="text-6xl font-bold text-gray-800 mb-4 font-mono tracking-wider">
                  {formatTime(currentTime)}
                </div>
                <div className="text-xl text-gray-600">
                  {formatDate(currentTime)}
                </div>
              </div>

              {/* Indicateurs visuels */}
              <div className="grid grid-cols-3 gap-4 mb-8">
                <div className="bg-gradient-to-br from-red-100 to-red-200 p-4 rounded-xl">
                  <div className="text-2xl font-bold text-red-800">
                    {currentTime.getHours().toString().padStart(2, '0')}
                  </div>
                  <div className="text-sm text-red-600 font-medium">Heures</div>
                </div>
                
                <div className="bg-gradient-to-br from-blue-100 to-blue-200 p-4 rounded-xl">
                  <div className="text-2xl font-bold text-blue-800">
                    {currentTime.getMinutes().toString().padStart(2, '0')}
                  </div>
                  <div className="text-sm text-blue-600 font-medium">Minutes</div>
                </div>
                
                <div className="bg-gradient-to-br from-gray-100 to-gray-200 p-4 rounded-xl">
                  <div className="text-2xl font-bold text-gray-800">
                    {currentTime.getSeconds().toString().padStart(2, '0')}
                  </div>
                  <div className="text-sm text-gray-600 font-medium">Secondes</div>
                </div>
              </div>

              {/* Informations supplémentaires */}
              <div className="bg-gradient-to-r from-red-50 to-blue-50 p-4 rounded-lg">
                <div className="grid grid-cols-2 gap-4 text-sm">
                  <div>
                    <span className="text-gray-600">Fuseau horaire :</span>
                    <div className="font-semibold text-gray-800">
                      {Intl.DateTimeFormat().resolvedOptions().timeZone}
                    </div>
                  </div>
                  <div>
                    <span className="text-gray-600">Semaine :</span>
                    <div className="font-semibold text-gray-800">
                      Semaine {Math.ceil((currentTime.getDate() + new Date(currentTime.getFullYear(), currentTime.getMonth(), 1).getDay()) / 7)}
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}

export default SectionHeurDate
