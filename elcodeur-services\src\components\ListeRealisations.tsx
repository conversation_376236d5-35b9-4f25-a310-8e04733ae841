import React, { useState } from 'react'
import DetailRealisation from './DetailRealisation'

interface Projet {
  id: number
  image: string
  alt: string
  category: string
  tech: string
  description: string
  dateRealisation: string
  client?: string
  statut: 'Terminé' | 'En cours' | 'En maintenance'
  descriptionComplete: string
  fonctionnalites: string[]
  technologies: string[]
  dureeProjet: string
  equipe: string[]
  defis: string[]
  resultats: string[]
  lienDemo?: string
  lienGithub?: string
}

interface ListeRealisationsProps {
  isOpen: boolean
  onClose: () => void
}

const ListeRealisations: React.FC<ListeRealisationsProps> = ({ isOpen, onClose }) => {
  const [selectedProjet, setSelectedProjet] = useState<Projet | null>(null)
  const [isDetailOpen, setIsDetailOpen] = useState(false)
  const projets: Projet[] = [
    {
      id: 1,
      image: '/1.jpg',
      alt: 'Site E-commerce - Boutique Mode',
      category: 'Site E-commerce',
      tech: 'React / Node.js / MongoDB',
      description: 'Plateforme e-commerce complète avec système de paiement sécurisé, gestion des stocks et interface d\'administration avancée.',
      dateRealisation: 'Mars 2024',
      client: 'Fashion Store SARL',
      statut: 'Terminé',
      descriptionComplete: 'Développement d\'une plateforme e-commerce moderne et complète pour Fashion Store SARL, spécialisée dans la vente de vêtements en ligne. Le projet incluait la création d\'un site web responsive avec un design élégant, un système de gestion des produits avancé, un panier d\'achat intuitif, et une intégration complète avec les systèmes de paiement sécurisés. L\'interface d\'administration permet une gestion complète des commandes, des stocks, et des clients.',
      fonctionnalites: [
        'Catalogue produits avec filtres avancés',
        'Panier d\'achat et wishlist',
        'Système de paiement sécurisé (Stripe, PayPal)',
        'Gestion des stocks en temps réel',
        'Interface d\'administration complète',
        'Système de notifications email',
        'Suivi des commandes',
        'Programme de fidélité',
        'Avis et évaluations clients',
        'Optimisation SEO'
      ],
      technologies: ['React.js', 'Node.js', 'Express.js', 'MongoDB', 'Stripe API', 'PayPal API', 'JWT', 'Tailwind CSS', 'Cloudinary'],
      dureeProjet: '3 mois',
      equipe: ['Développeur Full-Stack', 'Designer UI/UX', 'Chef de projet'],
      defis: [
        'Intégration de multiples systèmes de paiement',
        'Optimisation des performances pour un grand catalogue',
        'Sécurisation des données sensibles',
        'Responsive design pour tous les appareils'
      ],
      resultats: [
        'Augmentation des ventes de 150%',
        'Temps de chargement < 2 secondes',
        'Taux de conversion de 3.2%',
        '99.9% de disponibilité',
        'Interface mobile optimisée'
      ],
      lienDemo: 'https://demo-fashion-store.com',
      lienGithub: 'https://github.com/elcodeur/fashion-store'
    },
    {
      id: 2,
      image: '/2.jpg',
      alt: 'Application Mobile - Gestion Fitness',
      category: 'Application Mobile',
      tech: 'React Native / Firebase',
      description: 'Application mobile de suivi fitness avec programmes d\'entraînement personnalisés, tracking des performances et communauté sociale.',
      dateRealisation: 'Février 2024',
      client: 'FitLife Gym',
      statut: 'Terminé',
      descriptionComplete: 'Création d\'une application mobile complète pour FitLife Gym, permettant aux utilisateurs de suivre leurs entraînements, accéder à des programmes personnalisés et interagir avec une communauté fitness. L\'application inclut un système de tracking avancé, des vidéos d\'exercices, et une intégration avec les appareils de fitness connectés.',
      fonctionnalites: [
        'Programmes d\'entraînement personnalisés',
        'Suivi des performances et statistiques',
        'Bibliothèque d\'exercices avec vidéos',
        'Communauté sociale et défis',
        'Intégration appareils connectés',
        'Planificateur d\'entraînements',
        'Suivi nutritionnel',
        'Notifications motivationnelles'
      ],
      technologies: ['React Native', 'Firebase', 'Redux', 'Expo', 'React Navigation', 'Firebase Auth', 'Firestore', 'Cloud Functions'],
      dureeProjet: '4 mois',
      equipe: ['Développeur Mobile', 'Designer UI/UX', 'Spécialiste Fitness'],
      defis: [
        'Synchronisation temps réel des données',
        'Optimisation batterie pour le tracking',
        'Interface intuitive pour tous âges',
        'Intégration appareils tiers'
      ],
      resultats: [
        '10,000+ téléchargements en 3 mois',
        'Note moyenne 4.8/5 sur les stores',
        'Engagement utilisateur +200%',
        'Rétention à 30 jours: 65%'
      ],
      lienDemo: 'https://fitlife-app-demo.com'
    },
    {
      id: 3,
      image: '/3.jpg',
      alt: 'Système de Gestion - École',
      category: 'Système de Gestion',
      tech: 'Laravel / Vue.js / MySQL',
      description: 'Système complet de gestion scolaire avec modules élèves, professeurs, notes, emplois du temps et communication parents-école.',
      dateRealisation: 'Janvier 2024',
      client: 'Groupe Scolaire Excellence',
      statut: 'En maintenance',
      descriptionComplete: 'Développement d\'un système de gestion scolaire complet pour automatiser tous les processus administratifs et pédagogiques du Groupe Scolaire Excellence.',
      fonctionnalites: ['Gestion des élèves', 'Suivi des notes', 'Emplois du temps', 'Communication parents-école'],
      technologies: ['Laravel', 'Vue.js', 'MySQL', 'Bootstrap'],
      dureeProjet: '6 mois',
      equipe: ['Développeur Full-Stack', 'Analyste métier'],
      defis: ['Intégration systèmes existants', 'Formation utilisateurs'],
      resultats: ['Réduction temps admin 60%', 'Satisfaction utilisateurs 95%']
    },
    {
      id: 4,
      image: '/4.jpg',
      alt: 'Application - Suivi Médical',
      category: 'Application Web',
      tech: 'Vue.js / Laravel / MySQL',
      description: 'Plateforme de télémédecine avec prise de rendez-vous en ligne, dossiers médicaux numériques et consultations vidéo.',
      dateRealisation: 'Décembre 2023',
      client: 'Clinique Moderne',
      statut: 'Terminé',
      descriptionComplete: 'Plateforme de télémédecine moderne permettant aux patients de consulter des médecins à distance.',
      fonctionnalites: ['Prise de rendez-vous', 'Consultations vidéo', 'Dossiers médicaux'],
      technologies: ['Vue.js', 'Laravel', 'MySQL', 'WebRTC'],
      dureeProjet: '4 mois',
      equipe: ['Développeur Full-Stack', 'Expert sécurité'],
      defis: ['Conformité RGPD', 'Sécurité données médicales'],
      resultats: ['500+ consultations/mois', 'Temps attente réduit 70%']
    },
    {
      id: 5,
      image: '/5.jpg',
      alt: 'Site Vitrine - Restaurant',
      category: 'Site Vitrine',
      tech: 'WordPress / Custom Theme',
      description: 'Site vitrine élégant avec menu interactif, système de réservation en ligne et galerie photos haute qualité.',
      dateRealisation: 'Novembre 2023',
      client: 'Restaurant Le Gourmet',
      statut: 'Terminé',
      descriptionComplete: 'Site vitrine moderne pour restaurant avec réservation en ligne et présentation des menus.',
      fonctionnalites: ['Menu interactif', 'Réservation en ligne', 'Galerie photos'],
      technologies: ['WordPress', 'Custom Theme', 'PHP', 'JavaScript'],
      dureeProjet: '2 mois',
      equipe: ['Développeur WordPress', 'Designer'],
      defis: ['Design responsive', 'Optimisation SEO'],
      resultats: ['Réservations +300%', 'Visibilité web améliorée']
    },
    {
      id: 6,
      image: '/6.jpg',
      alt: 'Plateforme - Formation en Ligne',
      category: 'Plateforme E-learning',
      tech: 'Django / React / PostgreSQL',
      description: 'Plateforme complète de formation en ligne avec cours vidéo, quiz interactifs, certificats et suivi de progression.',
      dateRealisation: 'Octobre 2023',
      client: 'EduTech Solutions',
      statut: 'En cours',
      descriptionComplete: 'Plateforme e-learning complète avec cours vidéo et certifications.',
      fonctionnalites: ['Cours vidéo', 'Quiz interactifs', 'Certificats', 'Suivi progression'],
      technologies: ['Django', 'React', 'PostgreSQL', 'Redis'],
      dureeProjet: '5 mois',
      equipe: ['Développeur Full-Stack', 'Expert pédagogique'],
      defis: ['Streaming vidéo optimisé', 'Système de certification'],
      resultats: ['1000+ étudiants inscrits', 'Taux completion 85%']
    },
    {
      id: 7,
      image: '/7.jpg',
      alt: 'Application - Gestion Immobilière',
      category: 'Application Web',
      tech: 'Angular / .NET Core / SQL Server',
      description: 'Solution complète de gestion immobilière avec CRM intégré, visites virtuelles et outils de marketing digital.',
      dateRealisation: 'Septembre 2023',
      client: 'Immobilier Plus',
      statut: 'Terminé',
      descriptionComplete: 'Application web complète pour la gestion d\'agence immobilière.',
      fonctionnalites: ['CRM intégré', 'Visites virtuelles', 'Marketing digital'],
      technologies: ['Angular', '.NET Core', 'SQL Server', 'SignalR'],
      dureeProjet: '4 mois',
      equipe: ['Développeur .NET', 'Développeur Angular'],
      defis: ['Intégration CRM', 'Visites 360°'],
      resultats: ['Productivité +150%', 'Satisfaction client 98%']
    },
    {
      id: 8,
      image: '/8.jpg',
      alt: 'Site E-commerce - Produits Bio',
      category: 'Site E-commerce',
      tech: 'Shopify / Custom Development',
      description: 'Boutique en ligne spécialisée dans les produits bio avec système de traçabilité et programme de fidélité.',
      dateRealisation: 'Août 2023',
      client: 'Bio Nature',
      statut: 'Terminé',
      descriptionComplete: 'E-commerce spécialisé dans les produits biologiques.',
      fonctionnalites: ['Catalogue bio', 'Traçabilité produits', 'Programme fidélité'],
      technologies: ['Shopify', 'Liquid', 'JavaScript', 'Shopify API'],
      dureeProjet: '3 mois',
      equipe: ['Développeur Shopify', 'Designer'],
      defis: ['Système traçabilité', 'Intégration fournisseurs'],
      resultats: ['Ventes +250%', 'Clients fidèles +180%']
    },
    {
      id: 9,
      image: '/9.jpg',
      alt: 'Application - Gestion de Stock',
      category: 'Application Web',
      tech: 'React / Express.js / MongoDB',
      description: 'Système avancé de gestion des stocks avec codes-barres, alertes automatiques et rapports analytiques détaillés.',
      dateRealisation: 'Juillet 2023',
      client: 'Warehouse Solutions',
      statut: 'En maintenance',
      descriptionComplete: 'Système de gestion des stocks avec codes-barres et analytics.',
      fonctionnalites: ['Codes-barres', 'Alertes automatiques', 'Rapports analytiques'],
      technologies: ['React', 'Express.js', 'MongoDB', 'Chart.js'],
      dureeProjet: '3 mois',
      equipe: ['Développeur Full-Stack', 'Analyste données'],
      defis: ['Scan codes-barres', 'Rapports temps réel'],
      resultats: ['Erreurs stock -90%', 'Efficacité +200%']
    },
    {
      id: 10,
      image: '/10.jpg',
      alt: 'Site Corporate - Entreprise Tech',
      category: 'Site Corporate',
      tech: 'Next.js / Tailwind CSS',
      description: 'Site corporate moderne avec blog intégré, portfolio interactif et système de recrutement en ligne.',
      dateRealisation: 'Juin 2023',
      client: 'TechCorp International',
      statut: 'Terminé',
      descriptionComplete: 'Site corporate moderne avec fonctionnalités avancées.',
      fonctionnalites: ['Blog intégré', 'Portfolio interactif', 'Recrutement en ligne'],
      technologies: ['Next.js', 'Tailwind CSS', 'Prisma', 'PostgreSQL'],
      dureeProjet: '2 mois',
      equipe: ['Développeur React', 'Designer UI/UX'],
      defis: ['Performance SEO', 'Système recrutement'],
      resultats: ['Trafic +400%', 'Candidatures +300%']
    },
    {
      id: 11,
      image: '/11.jpg',
      alt: 'Application - Réseaux Sociaux',
      category: 'Application Mobile',
      tech: 'Flutter / Firebase / Cloud Functions',
      description: 'Réseau social de niche avec messagerie temps réel, partage de contenu multimédia et système de recommandations IA.',
      dateRealisation: 'Mai 2023',
      client: 'Social Connect',
      statut: 'En cours',
      descriptionComplete: 'Réseau social de niche avec IA et messagerie temps réel.',
      fonctionnalites: ['Messagerie temps réel', 'Partage multimédia', 'Recommandations IA'],
      technologies: ['Flutter', 'Firebase', 'Cloud Functions', 'TensorFlow'],
      dureeProjet: '6 mois',
      equipe: ['Développeur Mobile', 'Expert IA', 'Designer'],
      defis: ['Algorithme recommandations', 'Messagerie temps réel'],
      resultats: ['5000+ utilisateurs actifs', 'Engagement +180%']
    }
  ]

  const getStatutColor = (statut: string) => {
    switch (statut) {
      case 'Terminé':
        return 'bg-green-100 text-green-800'
      case 'En cours':
        return 'bg-blue-100 text-blue-800'
      case 'En maintenance':
        return 'bg-orange-100 text-orange-800'
      default:
        return 'bg-gray-100 text-gray-800'
    }
  }

  if (!isOpen) return null

  return (
    <div
      className="fixed inset-0 bg-white bg-opacity-95 z-50 flex items-center justify-center p-4 cursor-pointer"
      onClick={onClose}
    >
      <div
        className="bg-white rounded-2xl shadow-2xl w-full max-w-7xl max-h-[90vh] overflow-hidden cursor-default"
        onClick={(e) => e.stopPropagation()}
      >
        {/* En-tête */}
        <div className="bg-gradient-to-r from-red-800 to-blue-800 p-6 text-white">
          <div className="flex items-center justify-between">
            <div>
              <h2 className="text-3xl font-bold mb-2">Nos Réalisations</h2>
              <p className="text-blue-100">Portfolio complet de nos projets développés</p>
            </div>
            <button
              onClick={onClose}
              className="p-2 hover:bg-white hover:bg-opacity-20 rounded-full transition-all duration-300 cursor-pointer"
            >
              <i className="fas fa-times text-2xl"></i>
            </button>
          </div>
        </div>

        {/* Contenu scrollable */}
        <div className="p-6 overflow-y-auto max-h-[calc(90vh-120px)]">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {projets.map((projet) => (
              <div key={projet.id} className="bg-white rounded-xl shadow-lg border border-gray-100 overflow-hidden hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1">
                {/* Image */}
                <div className="relative h-48 overflow-hidden">
                  <img
                    src={projet.image}
                    alt={projet.alt}
                    className="w-full h-full object-cover transition-transform duration-300 hover:scale-110"
                  />
                  <div className="absolute top-3 right-3">
                    <span className={`px-3 py-1 rounded-full text-xs font-medium ${getStatutColor(projet.statut)}`}>
                      {projet.statut}
                    </span>
                  </div>
                </div>

                {/* Contenu */}
                <div className="p-5">
                  <div className="mb-3">
                    <h3 className="text-lg font-bold text-gray-800 mb-1">{projet.alt}</h3>
                    <span className="inline-block bg-blue-100 text-blue-800 px-3 py-1 rounded-full text-sm font-medium">
                      {projet.category}
                    </span>
                  </div>

                  <p className="text-gray-600 text-sm mb-4 line-clamp-3">
                    {projet.description}
                  </p>

                  <div className="space-y-2 mb-4">
                    <div className="flex items-center text-sm text-gray-500">
                      <i className="fas fa-code mr-2 text-red-600"></i>
                      <span className="font-medium">{projet.tech}</span>
                    </div>
                    <div className="flex items-center text-sm text-gray-500">
                      <i className="fas fa-calendar mr-2 text-blue-600"></i>
                      <span>{projet.dateRealisation}</span>
                    </div>
                    {projet.client && (
                      <div className="flex items-center text-sm text-gray-500">
                        <i className="fas fa-building mr-2 text-green-600"></i>
                        <span>{projet.client}</span>
                      </div>
                    )}
                  </div>

                  <button
                    onClick={() => {
                      setSelectedProjet(projet)
                      setIsDetailOpen(true)
                    }}
                    className="w-full bg-gradient-to-r from-red-600 to-blue-600 text-white py-2 px-4 rounded-lg font-medium hover:from-red-700 hover:to-blue-700 transition-all duration-300 transform hover:scale-105 cursor-pointer"
                  >
                    <i className="fas fa-eye mr-2"></i>
                    Voir les détails
                  </button>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* Modale Détail Réalisation */}
      <DetailRealisation
        isOpen={isDetailOpen}
        onClose={() => {
          setIsDetailOpen(false)
          setSelectedProjet(null)
        }}
        projet={selectedProjet}
      />
    </div>
  )
}

export default ListeRealisations
